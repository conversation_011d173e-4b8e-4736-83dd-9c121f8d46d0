package com.shuyun.fast.introspected;

import com.shuyun.cdp.tags.request.openapi.CustomerTagQueryRequest;
import com.shuyun.cdp.tags.request.openapi.CustomerTagsOperateRequest;
import com.shuyun.cdp.tags.request.openapi.CustomersTagOperateRequest;
import com.shuyun.epassport.sdk.Org;
import com.shuyun.fast.v1_0_0.param.guide.GuideChangeParam;
import com.shuyun.fast.v1_0_0.param.guide.GuideJobAndJurisdictionShopsParam;
import com.shuyun.fast.v1_0_0.param.guide.KylinGuideBindParam;
import com.shuyun.fast.v1_0_0.param.member.MobileEncryptParam;
import com.shuyun.kylin.member.api.request.*;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeModifyRequest;
import com.shuyun.loyalty.sdk.api.model.http.points.*;
import com.shuyun.ticket.benefit.vo.request.benefit.*;
import com.shuyun.ticket.benefit.vo.request.project.ProjectQueryVo;
import io.micronaut.core.annotation.Introspected;

@Introspected(classes = {ProjectQueryVo.class,
        BenefitAvailableListRequest.class,
        BenefitUseRequest.class,
        BenefitCancelUseRequest.class,
        BenefitDiscountCalcRequest.class,
        BenefitGrantRequest.class,
        BenefitDiscardRequest.class,
        BenefitLockRequest.class,
        BenefitReceiveRequest.class,
        BenefitReturnRequest.class,
        BenefitTransferRequest.class,
        BenefitUnlockRequest.class,
        BindRequest.class,
        MemberCancellationRequest.class,
        MemberQueryByIdentifyRequest.class,
        MemberQueryRequest.class,
        MemberMobileModifyRequest.class,
        MemberEditRequest.class,
        ChannelMemberEditRequest.class,
        RegisterRequest.class,
        ChannelMemberUnbindRequest.class,
        MemberPointDeductRequest.class,
        MemberPointSendRequest.class,
        MemberPointUnfreezeRequest.class,
        MemberPointRevertRequest.class,
        MemberPointFreezeRequest.class,
        MemberPointUseFrozenRequest.class,
        MemberQueryByCustomerNoRequest.class,
        MemberChannelQueryRequest.class,
        CustomersTagOperateRequest.class,
        CustomerTagsOperateRequest.class,
        CustomerTagQueryRequest.class,
        MemberGradeModifyRequest.class,
        MobileEncryptParam.class,
        MemberCreateRequest.class,
        Org.class,
        MemberPointImportRequest.class,
        KylinGuideBindParam.class,
        GuideChangeParam.class,
        GuideJobAndJurisdictionShopsParam.class

})
public class IntrospectedClass {
}
