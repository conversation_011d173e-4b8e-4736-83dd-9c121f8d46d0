package com.shuyun.fast.handler.api.v1_0_0.coupon;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.BenefitCache;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.param.coupon.CouponAvailableListParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.result.CouponGetResult;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.ticket.base.resource.dto.TicketIdentity;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitAvailableListRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitAvailableListResponse;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitResponse;
import io.micronaut.context.annotation.Requires;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class CouponAvailableListApiHandler extends AbstractApiHandler<CouponAvailableListParam, List<CouponGetResult>, BenefitAvailableListRequest, BenefitAvailableListResponse> {

    private final BenefitService benefitService;
    private final MemberService memberService;
    public CouponAvailableListApiHandler(MemberService memberService,
                                         BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponAvailableListParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public CouponAvailableListParam beforeRequest(CouponAvailableListParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public BenefitAvailableListRequest prepareParam(CouponAvailableListParam param) {
        BenefitCache cache = benefitService.bizCacheGet(param);
        BenefitAvailableListRequest request = new BenefitAvailableListRequest();
        request.setHolder(param.getIdentify().getMemberId());
        request.setTransactionId(param.getTransactionId());
        request.setProgramId(cache.getProgramId());
        request.setSubjectFqn(cache.getSubjectFqn());
        request.setExtension(param.getExtension());
        request.setCheckByOrderItem(param.getCheckByOrderItem());
        request.setOrder(param.getOrder());
        // request.setExtension(param.getExtension());
        log.info("traceId:{} 可用券列表查询入参1:{}", param.getTransactionId(), JSONObject.toJSONString(request));
        log.info("traceId:{} 可用券列表查询入参2:{}", param.getTransactionId(), JsonUtil.outPutSerialize(request));
        return request;
    }

    @Override
    public BenefitAvailableListResponse request(BenefitAvailableListRequest invokeParam) {
        return benefitService.availableList(invokeParam);
    }

    @Override
    public List<CouponGetResult> prepareResult(CouponAvailableListParam param, BenefitAvailableListResponse result) {
        // TODO: 2024/3/14 返回值封装
        BenefitCache cache = benefitService.bizCacheGet(param);
        List<CouponGetResult> list = new ArrayList<>();
        Map<String, BenefitResponse> map = result.getBenefits().stream()
                .collect(Collectors.toMap(BenefitResponse::getId, b->b));
        result.getResults().stream()
                .filter(r->r.getUsable())
                .forEach(r->{
                    TicketIdentity ticket = r.getIdentity();
                    BenefitProject project = benefitService.getProjectCache(cache.getProgramId(), ticket.getProjectId());
                    BenefitResponse benefit = map.get(ticket.getId());
                    CouponGetResult cr = JsonUtil.outPutConvert(benefit, CouponGetResult.class);
                    cr.setMemberId(param.getIdentify().getMemberId());
                    cr.setDenomination(r.getDenomination());
                    if(param.getShowProject()){
                        cr.setProject(JsonUtil.outPutConvert(project, CouponProject.class));
                    }
                    cr.setUsableOrderItemIds(r.getUsableOrderItemIds());
                    //填充项目信息
                    benefitService.fillProjectInfo(cr, project);
                    //填充选择器数据
                    // log.info("选择器参数填充:{}", JSONObject.toJSONString(param));
                    if (param.getIsSelector()) {
                        benefitService.fillSelectorData(param, cr, project);
                    }
                    list.add(cr);
        });
        return list;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_AVAILABLE_LIST;
    }
}
