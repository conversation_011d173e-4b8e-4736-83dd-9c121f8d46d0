package com.shuyun.fast.handler.api.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.tuple.Tuple2;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.ChannelMember;
import com.shuyun.fast.v1_0_0.param.member.MemberGetParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.result.MemberGetResult;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.kylin.member.api.response.MemberBindingQueryResult;
import com.shuyun.kylin.member.api.response.MemberQueryResult;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import com.shuyun.fast.handler.api.AbstractApiHandler;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Singleton
public class MemberGetApiHandler extends AbstractApiHandler<MemberGetParam, MemberGetResult, MemberGetParam, Tuple2<MemberQueryResult, MemberBindingQueryResult>> {
    private final MemberService memberService;

    public MemberGetApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }

    @Override
    public void validate(MemberGetParam param) {
        super.validate(param);

        MemberIdentifyParam identify = param.getIdentify();
        String mobile = param.getMobile();
        FastValidator.eitherOrValidate(identify, mobile, "identify","mobile");

        String memberId;
        String userId;
        if(Objects.nonNull(identify)){
            memberId = identify.getMemberId();
            userId = identify.getUserId();
            FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
        }

        //子类特性化校验
    }

    @Override
    public MemberGetParam beforeRequest(MemberGetParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public MemberGetParam prepareParam(MemberGetParam param) {
        List<String> optionalFields = param.getOptionalFields();
        if(CollectionUtils.isEmpty(optionalFields)){
            List<String> ops = new ArrayList<>();
            ops.add("registerShopCode");
            ops.add("registerShopName");
            param.setOptionalFields(ops);
        }else{
            optionalFields.add("registerShopCode");
            optionalFields.add("registerShopName");
            param.setOptionalFields(optionalFields);
        }
        return param;
    }

    @Override
    public Tuple2<MemberQueryResult, MemberBindingQueryResult> request(MemberGetParam invokeParam) {
        return memberService.get(invokeParam);
    }

    @Override
    public MemberGetResult prepareResult(MemberGetParam param, Tuple2<MemberQueryResult, MemberBindingQueryResult> result) {
        if(Objects.nonNull(result.first)) {
            if (StringUtils.isEmpty(result.first.getError_code()) && Objects.nonNull(result.first.getData())) {
                MemberGetResult r = JsonUtil.outPutConvert(result.first.getData(), MemberGetResult.class);
                r.setRegisterTime(DateUtil.localTime(result.first.getData().getEnrollTime()));
                r.setRegisterShopCode((String) result.first.getData().getOptionalFieldData().get("registerShopCode"));
                r.setRegisterShopName((String) result.first.getData().getOptionalFieldData().get("registerShopName"));
                if (param.getChannelDataRequired() && Objects.nonNull(result.second) && CollectionUtils.isNotEmpty(result.second.getData())) {
                    List<ChannelMember> list = JsonUtil.outPutListFromJson(JsonUtil.outPutSerialize(result.second.getData()), ChannelMember.class);
                    ChannelMember channelMember = list.get(0);
                    channelMember.setRegisterShopCode((String) channelMember.getOptionalFieldData().get("registerShopCode"));
                    channelMember.setRegisterShopName((String) channelMember.getOptionalFieldData().get("registerShopName"));
                    channelMember.setActionTime(DateUtil.localTime(result.second.getData().get(0).getTriggerTime()));
                    r.setChannelData(channelMember);
                }
                return r;
            }
            if (StringUtils.isEmpty(result.first.getError_code()) && Objects.isNull(result.first.getData())) {//查不到会员正常返回
                return null;
            }
            throw new ApiException(ApiTags.API_RESP_CODE_500203, result.first.getMsg());
        }else{
            return null;
        }
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_GET;
    }
}
