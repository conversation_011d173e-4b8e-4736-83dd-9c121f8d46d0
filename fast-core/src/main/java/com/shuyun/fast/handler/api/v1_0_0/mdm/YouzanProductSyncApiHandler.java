package com.shuyun.fast.handler.api.v1_0_0.mdm;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MdmService;
import com.shuyun.fast.v1_0_0.param.mdm.YouzanProductSyncParam;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;


@Slf4j
@Singleton
public class YouzanProductSyncApiHandler extends AbstractApiHandler<YouzanProductSyncParam, Void, YouzanProductSyncParam, Void> {
    private final MdmService mdmService;

    public YouzanProductSyncApiHandler(MdmService mdmService){
        this.mdmService = mdmService;
    }

    @Override
    public void validate(YouzanProductSyncParam param) {
        super.validate(param);
    }

    @Override
    public YouzanProductSyncParam beforeRequest(YouzanProductSyncParam param) {
        super.beforeRequest(param);
        //如果不是逻辑删除有赞商品信息 isValid=Y, productCodeList和 productName 必传
        if (!"N".equals(param.getIsValid())) {
            //如果不是逻辑删除有赞商品信息 isValid=Y, productCodeList和 productName 必传
            param.setIsValid("Y");
            if (param.getProductCodeList() == null || param.getProductCodeList().isEmpty() || StringUtils.isBlank(param.getProductName())){
                throw new ApiException(ApiTags.API_RESP_CODE_500100, "isValid字段非'N'时,productCodeList和productName不能为空值!");
            }
        }
        param.setChannelType(param.getRequestChannel());
        return param;
    }

    @Override
    public YouzanProductSyncParam prepareParam(YouzanProductSyncParam param) {
        // TODO: 2024/3/28 时区转换
        if (param.getCreateTime() == null) {
            param.setCreateTime(LocalDateTime.now());
        }
        if (param.getUpdateTime() == null) {
            param.setUpdateTime(LocalDateTime.now());
        }
        return param;
    }

    @Override
    public Void request(YouzanProductSyncParam invokeParam) {
        return mdmService.youzanProductSync(invokeParam);
    }

    @Override
    public Void prepareResult(YouzanProductSyncParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_YOUZAN_PRODUCT_SYNC;
    }
}
