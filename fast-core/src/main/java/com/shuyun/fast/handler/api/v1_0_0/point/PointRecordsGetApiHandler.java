package com.shuyun.fast.handler.api.v1_0_0.point;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.service.v1_0_0.PointService;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.v1_0_0.domain.PointRecord;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.point.PointRecordsGetParam;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRecordResponse;
import com.shuyun.ticket.util.JsonUtil;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Singleton
public class PointRecordsGetApiHandler extends AbstractApiHandler<PointRecordsGetParam, PageResult<PointRecord>, PointRecordsGetParam, List<MemberPointRecordResponse>> {

    private final MemberService memberService;
    private final PointService pointService;
    public PointRecordsGetApiHandler(MemberService memberService,
                                     PointService pointService){
        this.memberService = memberService;
        this.pointService = pointService;
    }

    @Override
    public void validate(PointRecordsGetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public PointRecordsGetParam beforeRequest(PointRecordsGetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public PointRecordsGetParam prepareParam(PointRecordsGetParam param) {
        return param;
    }

    @Override
    public List<MemberPointRecordResponse> request(PointRecordsGetParam invokeParam) {
        return pointService.pointRecordsGet(invokeParam);
    }

    @Override
    public PageResult<PointRecord> prepareResult(PointRecordsGetParam param, List<MemberPointRecordResponse> result) {
        List<PointRecord> records = new ArrayList<>();
        // TODO: 2024/3/15 时区转换
        result.forEach(r->{
            PointRecord record = JsonUtil.copyValue(r, PointRecord.class);
            record.setChangeTime(DateUtil.localTime(r.getCreated()));
            record.setEffectTime(DateUtil.localTime(r.getEffectiveDate()));
            record.setExpiredTime(DateUtil.localTime(r.getOverdueDate()));
            records.add(record);
        });
        PageResult<PointRecord> pageResult = new PageResult<>();
        pageResult.setPage(param.getPage());
        pageResult.setPageSize(param.getPageSize());
        // 查询记录总条数和计算总页数
        Long totalCount = pointService.pointRecordsCount(param);
        if (totalCount != null) {
            pageResult.setTotalCount(totalCount);
            pageResult.setTotalPage(totalCount % pageResult.getPageSize() == 0L ? (int)(totalCount / pageResult.getPageSize()) : (int)(totalCount/pageResult.getPageSize() + 1));
        } else {
            pageResult.setTotalCount(0L);
            pageResult.setTotalPage(0);
        }
        pageResult.setItems(records);
        return pageResult;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_POINT_RECORDS_GET;
    }
}
