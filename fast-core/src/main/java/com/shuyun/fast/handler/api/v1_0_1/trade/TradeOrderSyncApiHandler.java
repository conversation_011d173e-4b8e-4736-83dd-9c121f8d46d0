package com.shuyun.fast.handler.api.v1_0_1.trade;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.TradeCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.service.v1_0_1.TradeService;
import com.shuyun.fast.v1_0_0.constant.OrderOwnerType;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_1.param.trade.TradeOrderSyncParam;
import com.shuyun.fast.validator.FastValidator;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Slf4j
@Singleton
public class TradeOrderSyncApiHandler extends AbstractApiHandler<TradeOrderSyncParam, Void, TradeOrderSyncParam, Void> {

    private final TradeService tradeService;
    private final MemberService memberService;

    public TradeOrderSyncApiHandler(TradeService tradeService,
                                    MemberService memberService){
        this.tradeService = tradeService;
        this.memberService = memberService;
    }

    @Override
    public String apiVersion() {
        return ApiTags.API_VERSION_1_0_1;
    }

    @Override
    public void validate(TradeOrderSyncParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public TradeOrderSyncParam beforeRequest(TradeOrderSyncParam param) {
        super.beforeRequest(param);
        MemberIdentifyParam identify = param.getIdentify();
        if(StringUtils.isNotEmpty(identify.getMemberId())){//校验会员是否存在
            Boolean exist = memberService.memberExists(param, identify.getMemberId());
            param.getOrder().setOrderOwnerType(exist? OrderOwnerType.MEMBER : OrderOwnerType.CONSUMER);
        }else{
            try {
                param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
                param.getOrder().setOrderOwnerType(OrderOwnerType.MEMBER);
            } catch (ApiException e){
                param.getOrder().setOrderOwnerType(OrderOwnerType.CONSUMER);
                log.info("customerNo:{} find no member...", param.getIdentify().getUserId());
            }
        }
        // log.info("订单会员信息:{}", JSONObject.toJSONString(identify));
        TradeCache cache = tradeService.bizCacheGet(param);
        param.getOrder().setId(param.getOrder().getOrderId());
        param.getOrder().setMemberType(cache.getMemberType());
        if(OrderOwnerType.CONSUMER.equals(param.getOrder().getOrderOwnerType())){
            param.getOrder().setCustomerNo(StringUtils.isEmpty(param.getIdentify().getUserId()) ? identify.getMemberId() : param.getIdentify().getUserId());
            param.getOrder().setMemberId(null);
        }
        param.getOrder().setChannelType(param.getRequestChannel());
        param.getOrder().setShopTypeCode(param.getRequestChannel());
        if (param.getOrder().getUpdateTime() == null){
            param.getOrder().setUpdateTime(LocalDateTime.now());
        }
        param.getOrder().getOrderItems().forEach(item->{
            item.setId(item.getOrderItemId());
            item.setMemberType(cache.getMemberType());
            item.setCustomerNo(param.getOrder().getCustomerNo());
            item.setOrderId(param.getOrder().getOrderId());
            item.setShopCode(param.getOrder().getShopCode());
            item.setShopName(param.getOrder().getShopName());
            item.setChannelType(param.getRequestChannel());
            item.setShopTypeCode(param.getOrder().getShopTypeCode());
            item.setOrderOwnerType(param.getOrder().getOrderOwnerType());
            if (item.getTagPrice() == null && item.getRetailPrice() != null) {
                item.setTagPrice(item.getRetailPrice());
            }
            if (item.getRetailPrice() == null && item.getTagPrice() != null) {
                item.setRetailPrice(item.getTagPrice());
            }
            if (item.getFinishTime() == null && param.getOrder().getFinishTime() != null) {
                item.setFinishTime(param.getOrder().getFinishTime());
            }
            if (item.getUpdateTime() == null) {
                item.setUpdateTime(param.getOrder().getUpdateTime() == null ? LocalDateTime.now() : param.getOrder().getUpdateTime());
            }
        });
        param.getOrder().getPays().forEach(p->{
            p.setMemberType(cache.getMemberType());
            p.setOrderId(param.getOrder().getOrderId());
            p.setChannelType(param.getRequestChannel());
            p.setOrderOwnerType(param.getOrder().getOrderOwnerType());
        });
        param.getOrder().getCoupons().forEach(c->{
            c.setMemberType(cache.getMemberType());
            c.setOrderId(param.getOrder().getOrderId());
            c.setChannelType(param.getRequestChannel());
            c.setOrderOwnerType(param.getOrder().getOrderOwnerType());
        });
        return param;
    }

    @Override
    public TradeOrderSyncParam prepareParam(TradeOrderSyncParam param) {
        // TODO: 2024/3/29 时区转换
        return param;
    }

    @Override
    public Void request(TradeOrderSyncParam invokeParam) {
        return tradeService.orderSync(invokeParam);
    }

    @Override
    public Void prepareResult(TradeOrderSyncParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TRADE_ORDER_SYNC;
    }
}
