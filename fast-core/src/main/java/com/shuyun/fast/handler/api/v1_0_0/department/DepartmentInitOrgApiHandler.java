package com.shuyun.fast.handler.api.v1_0_0.department;

import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.DepartmentService;
import com.shuyun.fast.v1_0_0.param.department.DepartmentInitOrgParam;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class DepartmentInitOrgApiHandler extends AbstractApiHandler<DepartmentInitOrgParam, Void, DepartmentInitOrgParam, ApiResult<Void>> {

    @Inject
    private DepartmentService departmentService;

    @Override
    public void validate(DepartmentInitOrgParam param) {
        super.validate(param);
    }

    @Override
    public DepartmentInitOrgParam beforeRequest(DepartmentInitOrgParam param) {
        return super.beforeRequest(param);
    }

    @Override
    public DepartmentInitOrgParam prepareParam(DepartmentInitOrgParam param) {
        return param;
    }

    @Override
    public ApiResult<Void> request(DepartmentInitOrgParam param) {
        return departmentService.initOrg(param);
    }

    @Override
    public Void prepareResult(DepartmentInitOrgParam param, ApiResult<Void> apiResult) {
        if(ApiTags.API_RESP_SUCCESS.equals(apiResult.getCode())){
            return apiResult.getData();
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500100, apiResult.getMessage());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_DEPARTMENT_INIT_ORG;
    }
}
