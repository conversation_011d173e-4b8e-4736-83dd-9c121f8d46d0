package com.shuyun.fast.handler.api.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.BenefitCache;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.param.coupon.CouponListParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.result.CouponGetResult;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitResponse;
import io.micronaut.context.annotation.Requires;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class CouponListApiHandler extends AbstractApiHandler<CouponListParam, List<CouponGetResult>, CouponListParam, List<BenefitResponse>> {
    private final BenefitService benefitService;
    private final MemberService memberService;
    public CouponListApiHandler(MemberService memberService,
                                BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponListParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public CouponListParam beforeRequest(CouponListParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public CouponListParam prepareParam(CouponListParam param) {
        return param;
    }

    @Override
    public List<BenefitResponse> request(CouponListParam invokeParam) {
        return benefitService.list(invokeParam);
    }

    @Override
    public List<CouponGetResult> prepareResult(CouponListParam param, List<BenefitResponse> result) {
        BenefitCache cache = benefitService.bizCacheGet(param);
        return result.stream()
                .map(b->{
                    BenefitProject project = benefitService.getProjectCache(cache.getProgramId(), b.getProjectId());
                    CouponGetResult cr = JsonUtil.outPutConvert(b, CouponGetResult.class);
                    cr.setMemberId(param.getIdentify().getMemberId());
                    cr.setDenomination(b.getDenomination());
                    if(param.getShowProject()){
                        cr.setProject(JsonUtil.outPutConvert(project, CouponProject.class));
                    }
                    //填充项目信息
                    benefitService.fillProjectInfo(cr, project);
                    //填充选择器数据
                    // log.info("选择器参数填充:{}", JSONObject.toJSONString(param));
                    if (param.getIsSelector()) {
                        benefitService.fillSelectorData(param, cr, project);
                    }
                    return cr;
                })
                .collect(Collectors.toList());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_LIST;
    }
}
