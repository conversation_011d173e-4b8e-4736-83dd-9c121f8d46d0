package com.shuyun.fast.interceptor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiBaseParam;
import io.micronaut.aop.InterceptorBean;
import io.micronaut.aop.MethodInterceptor;
import io.micronaut.aop.MethodInvocationContext;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Singleton
@InterceptorBean(Api.class)
@Slf4j
public class ApiInterceptor implements MethodInterceptor {
    final private ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init(){
        SimpleFilterProvider defaultProvider = new SimpleFilterProvider();
        SimpleBeanPropertyFilter defaultFilter = SimpleBeanPropertyFilter.serializeAll();
        defaultProvider.addFilter("memberModifyParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmOrgSyncParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmProductSyncParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmShopSyncParamFilter", defaultFilter);
        defaultProvider.addFilter("youzanProductSyncParamFilter", defaultFilter);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setFilterProvider(defaultProvider);
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Override
    public Object intercept(MethodInvocationContext context) {
        String methodPath = String.join(".", context.getTarget().getClass().getName(), context.getTargetMethod().getName());
        Object param = context.getParameterValueMap().get("param");
        String traceId = "";
        if(Objects.nonNull(param) && param instanceof ApiBaseParam){
            traceId = ((ApiBaseParam) param).getTransactionId();
        }
        try {
            log.info("method path:{} traceId:{} api request:{}", methodPath, traceId, objectMapper.writeValueAsString(context.getParameterValueMap()));
        } catch (Exception e){
            log.info("error:", e);
        }

        long startTime = System.currentTimeMillis();
        Object result = context.proceed();
        try {
            log.info("method path:{} traceId:{} api response:{}", methodPath, traceId, objectMapper.writeValueAsString(result));
        } catch (Exception e){
            log.info("error:", e);
        }
        long duration = System.currentTimeMillis() - startTime;
        log.info("api 方法:{} 执行时间:{}ms", context.getTargetMethod(), duration);
        return result;
    }
}
