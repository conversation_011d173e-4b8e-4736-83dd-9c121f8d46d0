package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;

import javax.validation.constraints.NotEmpty;

@Tag(name = "redis管理")
@ExecuteOn("blocking")
@Controller("/v1/0.0/internal/redis")
@Slf4j
public class RedisResource {

    private final RedissonClient redissonClient;
    public RedisResource(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Get("/get")
    public ApiResult<Object> get(@QueryValue @NotEmpty String key,
                                 @QueryValue @NotEmpty String type) throws Exception{
        if("string".equals(type)){
            RBucket<Object> bucket = redissonClient.getBucket(key);
            log.info("redis key:{}...redis value:{}", key, bucket.get());
            return ApiResult.success(bucket.get());
        }else if("map".equals(type)){
            RMap<Object, Object> map = redissonClient.getMap(key);
            log.info("redis key:{}...redis value:{}", key, map.readAllMap());
            return ApiResult.success(map.readAllMap());
        }else if("list".equals(type)){
            RList<Object> list = redissonClient.getList(key);
            log.info("redis key:{}...redis value:{}", key, list.readAll());
            return ApiResult.success(list.readAll());
        }else{
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "type error");
        }

    }

}
