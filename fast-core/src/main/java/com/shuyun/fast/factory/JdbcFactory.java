package com.shuyun.fast.factory;

import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.transaction.jdbc.DelegatingDataSource;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

@Factory
public class JdbcFactory {
    
    @Bean
    public JdbcTemplate jdbcTemplate(DataSource dataSource){
        return new JdbcTemplate(DelegatingDataSource.unwrapDataSource(dataSource));
    }
}
