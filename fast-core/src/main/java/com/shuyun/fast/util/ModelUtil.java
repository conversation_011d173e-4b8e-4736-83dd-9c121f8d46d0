package com.shuyun.fast.util;

import com.shuyun.fast.v1_0_0.domain.TradeMainOrder;
import com.shuyun.fast.v1_0_0.domain.TradeMainOrderItem;
import io.micronaut.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Collections;

@Slf4j
public class ModelUtil {

    public static <T> String getFieldNames(String prefix, Class<T> clazz){
        Field[] fields = clazz.getDeclaredFields();
        String names = "";
        for(Field f:fields){
            if(!"extension".equals(f.getName())){
                String fieldName = StringUtils.isEmpty(prefix)?f.getName():prefix + "." + f.getName();
                names = String.join(",", names, fieldName);
            }
        }
        log.info("class :{} fields:{}", clazz, names.substring(1, names.length()));
        return names.substring(1, names.length());
    }

    public static void main(String[] args) {

//        ModelUtil.getFieldNames(TradeMainOrder.class);
        String fields = ModelUtil.getFieldNames("", TradeMainOrder.class).replace("orderItems", ModelUtil.getFieldNames("orderItems", TradeMainOrderItem.class));
        log.info("fields:{}", fields);
    }
}
