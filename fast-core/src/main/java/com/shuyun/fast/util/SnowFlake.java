package com.shuyun.fast.util;

import com.shuyun.lite.context.GlobalContext;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @Author: xiaolong.chang
 * @Date: 2021/4/10
 * @Description: twitter的snowflake算法
 * 使用时注意多实例下machineId不能相同,否则会出现重复
 */
@Singleton
@Slf4j
@Requires(property = "snowflake.enable", value = "true", defaultValue = "false")
public class SnowFlake {

    @Inject
    private RedissonClient redissonClient;

    private static final String tenantId = GlobalContext.defTenantId();
    @Value("${micronaut.application.name}")
    private String serviceId;

    @PostConstruct
    public void init(){
        machineId = initMachineId();
        scheduler.scheduleAtFixedRate(task, 0, 3, TimeUnit.MINUTES);
        log.info("===========holdMachineIdTask success=========period=" + (3 * 60 * 1000));
    }
    private Long initMachineId(){
        String lk = String.join("-", tenantId, serviceId, "snowFlakeMachineIdLock");
        RLock lock = redissonClient.getLock(lk);//加锁
        lock.lock(10, TimeUnit.MINUTES);
        Long machineId = 0L;
        try {
            Boolean result = false;
            do {
                ++machineId;
                String key = String.join("-", tenantId, serviceId, "snowFlakeMachineId" + machineId);
                RBucket<Long> keyObject = redissonClient.getBucket(key);
                Long value = keyObject.get();
                if(Objects.nonNull(value) && value>0){
                    log.info("machineId:{} has be used", machineId);
                    continue;
                }
                result = keyObject.trySet(machineId, 10, TimeUnit.MINUTES);//设置10分钟

            } while (!result);
            log.info("get machineId:{} success", machineId);

        } catch (Exception e){
            log.info("initMachineId error:", e);
        } finally {
            lock.unlock();
        }
        return machineId;
    }

    public class HoldMachineIdTask implements Runnable{
        @Override
        public void run() {
            String key = String.join("-", tenantId, serviceId, "snowFlakeMachineId" + machineId);
            RBucket<Long> keyObject = redissonClient.getBucket(key);
            keyObject.set(machineId, 10, TimeUnit.MINUTES);//设置10分钟
            log.info("holdMachineIdTask....key:{}...result:{}", key);
        }
    }

    private HoldMachineIdTask task = new HoldMachineIdTask();

    final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * 起始的时间戳
     */
    private final static long START_STMP = 1618042992000L;//2021-04-10 16:23:12

    /**
     * 每一部分占用的位数
     */
    private final static long SEQUENCE_BIT = 14; //序列号占用的位数
    private final static long MACHINE_BIT = 5;   //机器标识占用的位数
    private final static long DATACENTER_BIT = 3;//数据中心占用的位数

    /**
     * 每一部分的最大值
     */
    private final static long MAX_DATACENTER_NUM = -1L ^ (-1L << DATACENTER_BIT);
    private final static long MAX_MACHINE_NUM = -1L ^ (-1L << MACHINE_BIT);
    private final static long MAX_SEQUENCE = -1L ^ (-1L << SEQUENCE_BIT);

    /**
     * 每一部分向左的位移
     */
    private final static long MACHINE_LEFT = SEQUENCE_BIT;
    private final static long DATACENTER_LEFT = SEQUENCE_BIT + MACHINE_BIT;
    private final static long TIMESTMP_LEFT = DATACENTER_LEFT + DATACENTER_BIT;

    private long datacenterId = 2L;  //数据中心
    private long machineId;     //机器标识
    private long sequence = 0L; //序列号
    private long lastStmp = -1L;//上一次时间戳

//    public SnowFlake(long datacenterId, long machineId) {
//        if (datacenterId > MAX_DATACENTER_NUM || datacenterId < 0) {
//            throw new IllegalArgumentException("datacenterId can't be greater than MAX_DATACENTER_NUM or less than 0");
//        }
//        if (machineId > MAX_MACHINE_NUM || machineId < 0) {
//            throw new IllegalArgumentException("machineId can't be greater than MAX_MACHINE_NUM or less than 0");
//        }
//        this.datacenterId = datacenterId;
//        this.machineId = machineId;
//    }

//    public SnowFlake(long datacenterId) {
//        if (datacenterId > MAX_DATACENTER_NUM || datacenterId < 0) {
//            throw new IllegalArgumentException("datacenterId can't be greater than MAX_DATACENTER_NUM or less than 0");
//        }
//        if (machineId > MAX_MACHINE_NUM || machineId < 0) {
//            throw new IllegalArgumentException("machineId can't be greater than MAX_MACHINE_NUM or less than 0");
//        }
//        this.datacenterId = datacenterId;
//        this.machineId = ;
//    }

    /**
     * 产生下一个ID
     *
     * @return
     */
    public synchronized long nextId() {
        long currStmp = getNewstmp();
        if (currStmp < lastStmp) {
            throw new RuntimeException("Clock moved backwards.  Refusing to generate id");
        }

        if (currStmp == lastStmp) {
            //相同毫秒内，序列号自增
            sequence = (sequence + 1) & MAX_SEQUENCE;
            //同一毫秒的序列数已经达到最大
            if (sequence == 0L) {
                currStmp = getNextMill();
            }
        } else {
            //不同毫秒内，序列号置为0
            sequence = 0L;
        }

        lastStmp = currStmp;

        return (currStmp - START_STMP) << TIMESTMP_LEFT //时间戳部分
                | datacenterId << DATACENTER_LEFT       //数据中心部分
                | machineId << MACHINE_LEFT             //机器标识部分
                | sequence;                             //序列号部分
    }

    private long getNextMill() {
        long mill = getNewstmp();
        while (mill <= lastStmp) {
            mill = getNewstmp();
        }
        return mill;
    }

    private long getNewstmp() {
        return System.currentTimeMillis();
    }

    public static void main(String[] args) {

    }
}
