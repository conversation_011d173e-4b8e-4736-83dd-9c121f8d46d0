package com.shuyun.fast.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhuqi on 2017/11/13.
 */
@Slf4j
public class SignConvertUtil {

    // 默认使用saas channel服务账号进行 api-gateway 校验
    private static String callerService = "channel";
    private static String secret = "636867NETCylPyKNTFlkl";

    @Deprecated
    public static MultiValueMap<String,Object> generateHeader(String url) {
        log.info("产生header信息开始.url:{}",url);
        MultiValueMap headerMap = new LinkedMultiValueMap();
        int index = url.indexOf("//");
        String uriPath = url;
        String serviceName = null;
        String version = null;
        StringBuffer  requestPath = new StringBuffer();
        if(index > 0){ //若包含http或https,则排除http:// or https://
            uriPath = url.substring(index+2);
        }
        //去除url中之后的参数
        if(uriPath.indexOf("?")!=-1&&index > 0){
            index = uriPath.indexOf("?");
        }else{
            index=uriPath.length();
        }
        uriPath = uriPath.substring(0,index);
        String[] paths = uriPath.split("/");
        serviceName = paths[1];
        version = paths[2];

        for(int i = 3; i< paths.length;i++){
            requestPath.append("/").append(paths[i]);
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = sf.format(new Date());
        log.info("产生header信息.获取apigateway head信息结束. callerService:{},secret:{} ",callerService,secret);
        headerMap.add("X-Caller-Service",callerService);
        headerMap.add("X-Caller-Timestamp","2022-12-19 14:24:54");
        String sign = generateSign(callerService,serviceName,version,time,secret,requestPath.toString());
        headerMap.add("X-Caller-Sign",sign);
        return headerMap;
    }

    public static MultiValueMap<String, String> generateHeader(String url, String callerService, String secret) {
        log.info("产生header信息开始.url:{}", url);
        MultiValueMap headerMap = new LinkedMultiValueMap<>();
        int index = url.indexOf("//");
        String uriPath = url;
        String serviceName = null;
        String version = null;
        StringBuffer requestPath = new StringBuffer();
        if(index > 0) {
            uriPath = url.substring(index + 2);
        }

        if(uriPath.indexOf("?") != -1 && index > 0) {
            index = uriPath.indexOf("?");
        } else {
            index = uriPath.length();
        }
        uriPath = uriPath.substring(0, index);
        String[] paths = uriPath.split("/");
        serviceName = paths[1];
        version = paths[2];

        for(int i = 3; i < paths.length; ++i) {
            requestPath.append("/").append(paths[i]);
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = sf.format(new Date());
        log.info("产生header信息.获取apigateway head信息结束. callerService:{},secret:{} ", callerService, secret);
        headerMap.add("X-Caller-Service", callerService);
        headerMap.add("X-Caller-Timestamp", time);
        String sign = generateSign(callerService, serviceName, version,time , secret, requestPath.toString());
        headerMap.add("X-Caller-Sign", sign);
        headerMap.add("Content-Type", "application/json");
        return headerMap;
    }
    public static String generateSign(String callerService, String contextPath, String version, String timestamp, String serviceSecret, String requestPath) {
        log.info("产生签名开始callerService:{},contextPath:{},version:{},timestamp:{},serviceSecret:{},requestPath:{}",
                callerService,contextPath,version,timestamp,serviceSecret,requestPath);
        String sign = "";
        if (callerService == null || callerService.equals("") || contextPath == null || contextPath.equals("")
                || timestamp == null || timestamp.equals("") || serviceSecret == null || serviceSecret.equals("")) {
            return sign;
        }
        Map<String, String> map = new LinkedHashMap<>();
        map.put("callerService", callerService);
        map.put("contextPath", contextPath);
        try {
            map.put("requestPath", requestPath);
            map.put("timestamp", timestamp);
            map.put("v", version);
            sign = generateMD5Sign(serviceSecret, map).toUpperCase();
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            e.printStackTrace();
            return "";
        }
        return sign;
    }


    private static String generateMD5Sign(String secret, Map<String, String> parameters) throws NoSuchAlgorithmException,       UnsupportedEncodingException {
        return generateConcatSign(secret,parameters);
    }

    private static String generateConcatSign(String secret, Map<String, String> parameters) {
        StringBuilder sb = new StringBuilder().append(secret);
        Set<String> keys = parameters.keySet();
        for (String key : keys) {
            sb.append(key).append(parameters.get(key));
        }
        log.info("加密前：{}",sb.append(secret));
        String encryption = Md5.encryption(String.valueOf(sb)).toUpperCase();
        log.info("加密后：{}",encryption);
        return encryption;
    }

    private static String byteToHex(byte[] bytesIn) {
        StringBuilder sb = new StringBuilder();
        for (byte byteIn : bytesIn) {
            String bt = Integer.toHexString(byteIn & 0xff);
            if (bt.length() == 1)
                sb.append(0).append(bt);
            else
                sb.append(bt);
        }
        return sb.toString().toUpperCase();
    }



    /**
     * 签名
     *
     * @param callerService 服务名
     * @param contextPath   根路径
     * @param version       版本号
     * @param timestamp     时间戳
     * @param serviceSecret 服务密钥
     * @param requestPath   请求路径
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/22 11:03
     **/
    public static String generateCrmSign(String callerService, String contextPath, String version, String timestamp, String serviceSecret, String requestPath) {
        String sign = "";
        if (StringUtils.isEmpty(callerService) || StringUtils.isEmpty(contextPath) ||
                StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(serviceSecret)) {
            return sign;
        }
        Map<String, String> map = new LinkedHashMap<>();
        map.put("callerService", callerService);
        map.put("contextPath", contextPath);
        try {
            if (requestPath != null) {
                StringBuilder sb = new StringBuilder();
                for (String part : requestPath.split("/")) {
                    sb.append("/").append(URLEncoder.encode(part, "utf-8"));
                }
                map.put("requestPath", sb.toString().substring(1));
            }
            map.put("timestamp", timestamp);
            map.put("v", version);
            sign = generateMD5Sign(serviceSecret, map);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            e.printStackTrace();
            return "";
        }
        return sign;
    }

}
