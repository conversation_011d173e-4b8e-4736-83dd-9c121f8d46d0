package com.shuyun.fast.util;

import com.shuyun.dm.dataapi.sdk.DataapiSdkFactory;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DataapiSdkUtil {
    public static DataapiHttpSdk getDataapiHttpSdk(){
        return dataapiHttpSdk;
    }

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk();
}
