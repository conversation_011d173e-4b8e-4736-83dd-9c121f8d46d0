package com.shuyun.fast.config;

import io.micronaut.context.annotation.ConfigurationProperties;
import lombok.Data;

/**
 * <AUTHOR>
 * @title BojunConfiguration
 * @description 伯俊配置类
 * @create 2024/9/11 16:11
 */
@Data
@ConfigurationProperties("bojun.interface")
public class BojunConfiguration {
    private String userName;
    private String userKey;
    private String requestSign;
    private String url;
}
