package com.shuyun.fast.service.v1_0_0;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.cache.v1_0_0.MbspCache;
import com.shuyun.fast.client.v1_0_0.EbrandMemberClient;
import com.shuyun.fast.client.v1_0_0.MbspClient;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.tuple.Tuple2;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.util.SnowFlake;
import com.shuyun.fast.v1_0_0.domain.DynamicCode;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.*;
import com.shuyun.fast.v1_0_0.result.MobileEncryptResult;
import com.shuyun.kylin.member.api.enums.ChannelMemberStatusEnum;
import com.shuyun.kylin.member.api.request.*;
import com.shuyun.kylin.member.api.response.*;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.pip.util.ObjectUtils;
import com.shuyun.ticket.base.domain.TicketState;
import com.shuyun.ticket.benefit.domain.Benefit;
import com.shuyun.ticket.benefit.vo.request.benefit.batchImport.BenefitBatchImportRequest;
import com.shuyun.ticket.benefit.vo.request.benefit.batchImport.BenefitImportRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.batchImport.BenefitBatchImportResponse;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Singleton
@Slf4j
public class MemberService {
    private final MbspClient mbspClient;
    private final BizCacheService bizCacheService;
    private final EbrandMemberClient ebrandClient;

    private final BenefitService benefitService;

    @Value("${member.ebrand.enable}")
    private Boolean ebrandEnable;//是否开启会员通

    private final SnowFlake snowFlake;
    private final RedissonClient redissonClient;

    private static final String tenantId = GlobalContext.defTenantId();

    private final JdbcTemplate jdbcTemplate;

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
    public MemberService(MbspClient mbspClient,
                         BizCacheService bizCacheService,
                         EbrandMemberClient ebrandClient, BenefitService benefitService,
                         SnowFlake snowFlake,
                         RedissonClient redissonClient, JdbcTemplate jdbcTemplate){
        this.mbspClient = mbspClient;
        this.bizCacheService = bizCacheService;
        this.ebrandClient = ebrandClient;
        this.benefitService = benefitService;
        this.snowFlake = snowFlake;
        this.redissonClient = redissonClient;
        this.jdbcTemplate = jdbcTemplate;
    }

    public MbspCache bizCacheGet(ApiBaseParam param){
        MbspCache cache = bizCacheService.get(MbspCache.class, tenantId, param.getBizCode(), BizCache.MBSP)
                .stream()
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.MBSP);
        }
        return cache;
    }

    public Boolean memberExists(ApiBaseParam param, String memberId){
        MbspCache cache = bizCacheGet(param);
        MemberQueryRequest request = new MemberQueryRequest();
        request.setProgramCode(cache.getProgramId());
        request.setEnableCancellation(false);
        request.setMemberId(memberId);
        MemberQueryResult result = mbspClient.query(request);
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            return StringUtils.isNotEmpty(result.getData().getMemberId());
        }
        return false;
    }

    /**
     * 查询会员id,mobile最高优先级
     * 本方法考虑性能可以使用实例级缓存，但需要考虑手机号、渠道id解绑场景的对应的缓存失效场景
     * @param tenantId 租户id
     * @param bizCode 业务编码
     * @param channel
     * @param userId
     * @return
     */
    public String getMemberId(String tenantId, String bizCode, String channel, String userId){
        log.info("get memberId from mbsp module by userId:{}", userId);
        ApiBaseParam param = new MemberGetParam();
        param.setTenantId(tenantId);
        param.setBizCode(bizCode);
        MbspCache cache = bizCacheGet(param);
        MemberQueryByCustomerNoRequest request = new MemberQueryByCustomerNoRequest();
        request.setProgramCode(cache.getProgramId());
        request.setEnrollChannel(channel);
        request.setCustomerNo(userId);
        MemberQueryResult result = mbspClient.queryMemberInfoByChannelId(request);
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            return result.getData().getMemberId();
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500203, result.getMsg());
    }

    public MemberIdentifyParam memberIdentify(MemberIdentifyParam identify,  ApiBaseParam param) throws ApiException{
        if(StringUtils.isNotEmpty(identify.getMemberId())){
            return identify;
        }
        String userId = identify.getUserId();
        String channel = identify.getChannel();
        if(StringUtils.isEmpty(channel)){
            identify.setChannel(param.getRequestChannel());
        }
        String memberId = getMemberId(tenantId, param.getBizCode(), identify.getChannel(), userId);
        if(StringUtils.isNotEmpty(memberId)){
            identify.setMemberId(memberId);
            return identify;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500000, userId);
    }

    public List<String> mobileEncrypt(String mobile, String programId){
        MobileEncryptParam encryptParam = new MobileEncryptParam();
        encryptParam.setMobile(mobile);
        encryptParam.setModelUnit(programId);
        MobileEncryptResult encryptResult = ebrandClient.encrypt(encryptParam);
        if(!CollectionUtils.isEmpty(encryptResult.getMixMobiles())){
            //密文手机号添加
            return encryptResult.getMixMobiles()
                    .stream()
                    .map(m->m.getMixMobile())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
    public Tuple2<MemberQueryResult, MemberBindingQueryResult> get(MemberGetParam param) {
        MbspCache cache = bizCacheGet(param);
        MemberIdentifyParam identify = param.getIdentify();
        MemberQueryResult memberQueryResult = null;
        MemberBindingQueryResult bindingQueryResult = null;
        if(StringUtils.isNotEmpty(param.getMobile())){//根据手机号查询,如果有会员通需要先请求会员通服务拿密文(淘宝密文会员匹配)
            MemberQueryByIdentifyRequest request = new MemberQueryByIdentifyRequest();
            request.setProgramCode(cache.getProgramId());
            List<String> mobileValues = new ArrayList<>();
            mobileValues.add(param.getMobile());
            if(ebrandEnable){
                mobileValues.addAll(mobileEncrypt(param.getMobile(), cache.getProgramId()));
            }
            MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
            identifyInfo.setCode("mobile");
            identifyInfo.setValues(mobileValues);
            request.setIdentifies(Collections.singletonList(identifyInfo));
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                request.setOptionalFields(param.getOptionalFields());
            }
            memberQueryResult = mbspClient.queryByIdentify(request);
        } else if(StringUtils.isNotEmpty(identify.getMemberId())){//根据memberId查询
            MemberQueryRequest request = new MemberQueryRequest();
            request.setProgramCode(cache.getProgramId());
            request.setEnableCancellation(false);
            request.setMemberId(param.getIdentify().getMemberId());
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                request.setOptionalFields(param.getOptionalFields());
            }
            memberQueryResult = mbspClient.query(request);
        } else{//根据userId查询
            MemberQueryByCustomerNoRequest request = new MemberQueryByCustomerNoRequest();
            request.setProgramCode(cache.getProgramId());
            request.setEnrollChannel(param.getRequestChannel());
            request.setCustomerNo(identify.getUserId());
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                request.setOptionalFields(param.getOptionalFields());
            }
            memberQueryResult = mbspClient.queryMemberInfoByChannelId(request);
        }
        if(param.getChannelDataRequired() && Objects.nonNull(memberQueryResult.getData())){//查询渠道数据
            MemberChannelQueryRequest request = new MemberChannelQueryRequest();
            request.setEnableCancellation(false);
            if(Objects.nonNull(identify) && StringUtils.isNotEmpty(identify.getUserId())){
                request.setCustomerNo(identify.getUserId());
            }
            request.setMemberId(memberQueryResult.getData().getMemberId());
            request.setProgramCode(cache.getProgramId());
            request.setEnrollChannel(param.getRequestChannel());
            request.setChannelStatus(ChannelMemberStatusEnum.ACTIVE.name());
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                request.setOptionalFields(param.getOptionalFields());
            }
            bindingQueryResult = mbspClient.queryChannelMembers(request);
        }
        if(!ObjectUtils.isEmpty(memberQueryResult) && !ObjectUtils.isEmpty(memberQueryResult.getData())){
            if(StringUtils.isEmpty(memberQueryResult.getData().getMobile())){
                memberQueryResult=null;
            }
        }
        return new Tuple2<>(memberQueryResult, bindingQueryResult);
    }

    public BindResult bind(MemberBindParam param) {
        MbspCache cache = bizCacheGet(param);
        List<String> mobileValues = new ArrayList<>();
        mobileValues.add(param.getMobile());
        if(ebrandEnable){
            mobileValues.addAll(mobileEncrypt(param.getMobile(), cache.getProgramId()));
        }
        BindRequest request = JsonUtil.convert(param, BindRequest.class);
        request.setProgramCode(cache.getProgramId());
        request.setBrand(cache.getBrand());
        request.setCustomerNo(param.getIdentify().getUserId());
        request.setMemberId(param.getIdentify().getMemberId());
        MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
        identifyInfo.setCode("mobile");
        identifyInfo.setValues(mobileValues);
        request.setIdentifies(Collections.singletonList(identifyInfo));
        request.setEnrollChannel(param.getRegisterChannel());
        request.setEnrollShopCode(param.getRegisterShopCode());
        request.setEnrollShopName(param.getRegisterShopName());
        request.setEnrollGuide(param.getRegisterGuide());
        // TODO: 2024/3/14 特殊字段映射
        return mbspClient.bind(request);
    }

    public RegisterResult register(MemberRegisterParam param) {
        MbspCache cache = bizCacheGet(param);
        List<String> mobileValues = new ArrayList<>();
        mobileValues.add(param.getMobile());
        if(ebrandEnable){
            mobileValues.addAll(mobileEncrypt(param.getMobile(), cache.getProgramId()));
        }
        RegisterRequest request = JsonUtil.convert(param, RegisterRequest.class);
        request.setProgramCode(cache.getProgramId());
        request.setBrand(cache.getBrand());
        request.setCustomerNo(param.getIdentify().getUserId());
        MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
        identifyInfo.setCode("mobile");
        identifyInfo.setValues(mobileValues);
        request.setIdentifies(Collections.singletonList(identifyInfo));
        request.setEnrollTime(DateUtil.utcTime(param.getRegisterTime()));
        request.setEnrollChannel(param.getRegisterChannel());
        request.setEnrollShopCode(param.getRegisterShopCode());
        request.setEnrollShopName(param.getRegisterShopName());
        request.setEnrollGuide(param.getRegisterGuide());
        // TODO: 2024/3/14  特殊字段映射
        return mbspClient.register(request);
    }

    public MemberBaseResult unbind(ChannelMemberUnbindRequest param) {
        return mbspClient.unbindChannelMember(param);
    }

    public MemberBaseResult deregister(MemberCancellationRequest param) {
        return mbspClient.memberCancellation(param);
    }

    public MemberUpdateResult modify(ChannelMemberEditRequest param) {
        return mbspClient.updateChannelMember(param);
    }

    public MemberBaseResult mobileModify(MemberMobileModifyParam param) {
        MbspCache cache = bizCacheGet(param);
        MemberMobileModifyRequest request = JsonUtil.convert(param, MemberMobileModifyRequest.class);
        request.setProgramCode(cache.getProgramId());
        request.setMemberId(param.getIdentify().getMemberId());
        request.setNewMobile(param.getMobile());
        request.setChannelType(param.getRequestChannel());
        List<String> mobileValues = new ArrayList<>();
        mobileValues.add(param.getMobile());
        if(ebrandEnable){
            mobileValues.addAll(mobileEncrypt(param.getMobile(), cache.getProgramId()));
        }
        request.setNewMixMobiles(mobileValues);
        return mbspClient.modifyMobile(request);
    }

    public DynamicCode dynamicCodeGet(MemberDynamicCodeGetParam param) {
        String memberId = param.getIdentify().getMemberId();
        String code = String.valueOf(snowFlake.nextId());
        RBucket<String> bucket = redissonClient.getBucket(code);
        bucket.set(memberId, param.getValidSeconds(), TimeUnit.SECONDS);//设置有效期
        return new DynamicCode(code);
    }

    public MemberId dynamicCodeIdentify(MemberDynamicCodeIdentifyParam param) {
        RBucket<String> bucket = redissonClient.getBucket(param.getDynamicCode());
        MemberId m = new MemberId();
        m.setMemberId(bucket.get());
        return m;
    }

    public void createHisMember(JSONObject param) {
        com.alibaba.fastjson.JSONObject event = (com.alibaba.fastjson.JSONObject) com.alibaba.fastjson.JSONObject.toJSON(param);
       // JSONObject event = JSONUtil.parseObj(value);
        String id = event.getString("id");
        String memberId = ObjectUtils.isEmpty(event.getString("memberId")) ? null : event.getString("memberId");
        //String memberId = event.getStr("memberId");
        String bizCode = event.getString("brand");
       // MbspCache cache = bizCacheGet(bizCode);
      //  log.info("开始处理微信历史会员memberId:{} 信息:{}", memberId, event);
        MemberQueryRequest  quryRequest = new MemberQueryRequest();
        quryRequest.setMemberId(memberId);
        quryRequest.setProgramCode(bizCode);
       // log.info("查询微信历史会员memberId:{}是否已经创建......", memberId,bizCode);
        MemberQueryResult queryResult = mbspClient.query(quryRequest);
       // log.info("微信历史会员memberId:{}查询返回结果:{}", memberId, JSON.toJSONString(queryResult));
        if(null != queryResult && null != queryResult.getData()){
          //  log.info("微信历史会员memberId:{}已经创建成功,走会员注册接口......", memberId);
            RegisterRequest registerParam = JsonUtil.convert(event, RegisterRequest.class);
            registerParam.setProgramCode(bizCode);
            // 明文手机号加密
            List<String> mobileValues = new ArrayList<>();
            if(StringUtils.isEmpty(registerParam.getMobile())){
              mobileValues.addAll(Arrays.asList(registerParam.getMixMobile()));
            }else {
              mobileValues.add(registerParam.getMobile());
              mobileValues.addAll(mobileEncrypt(registerParam.getMobile(), bizCode));
            }
            MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
            identifyInfo.setCode("mobile");
            identifyInfo.setValues(mobileValues);
            registerParam.setIdentifies(Collections.singletonList(identifyInfo));
            try {
              //  log.info("微信历史会员memberId:{}注册参数:{}", memberId, JSON.toJSONString(registerParam));
                RegisterResult registerResult = mbspClient.register(registerParam);
                log.info("微信历史会员memberId:{}注册返回结果:{}", memberId, JSON.toJSONString(registerResult));
                if("success".equalsIgnoreCase(registerResult.getMsg())){
                  /*  Map<String, Object> map = new HashMap<>();
                    map.put("id", id);
                    map.put("isSuccess", "Y");
                    map.put("memberId", registerResult.getData().getMemberId());
                    dataapiHttpSdk.update(ModelTags.DATA_FQN_HIS_MEMBER_WECHAT, id, map, false);*/
                    String sql = String.format(
                            "update %s  set  isSuccess= 'Y',kylinMemberId='%s'  where id ='%s'",
                            event.getString("table"),registerResult.getData().getMemberId(),id);
                    jdbcTemplate.update(sql);
                } else {
                  //  log.warn("微信历史会员memberId:{}注册失败!", memberId);
                   /* Map<String, Object> map = new HashMap<>();
                    map.put("id", id);
                    map.put("isSuccess", "F");
                    dataapiHttpSdk.update(ModelTags.DATA_FQN_HIS_MEMBER_WECHAT, id, map, false);*/
                    String sql = String.format(
                            "update %s  set  isSuccess= 'N' where id ='%s'",
                            event.getString("table"),id);
                    jdbcTemplate.update(sql);
                }
            } catch (Exception e) {
               // log.error("微信历史会员:{} 注册异常:{}", memberId, e.getMessage());
               /* Map<String, Object> map = new HashMap<>();
                map.put("id", id);
                map.put("isSuccess", "F");
                dataapiHttpSdk.update(ModelTags.DATA_FQN_HIS_MEMBER_WECHAT, id, map, false);*/
                String sql = String.format(
                        "update %s  set  isSuccess= 'N'  where id ='%s'",
                        event.getString("table"),id);
                jdbcTemplate.update(sql);
            }
        } else{
           // log.info("微信历史会员memberId:{}没有创建,走会员创建接口......", memberId);
            MemberCreateRequest createRequest = JsonUtil.convert(event, MemberCreateRequest.class);
            createRequest.setProgramCode(bizCode);
            // 明文手机号加密
            List<String> mobileValues = new ArrayList<>();
            if(StringUtils.isEmpty(createRequest.getMobile())){
                mobileValues.addAll(Arrays.asList(createRequest.getMixMobile()));
            }else {
                mobileValues.add(createRequest.getMobile());
                mobileValues.addAll(mobileEncrypt(createRequest.getMobile(), bizCode));
            }
            MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
            identifyInfo.setCode("mobile");
            identifyInfo.setValues(mobileValues);
            createRequest.setIdentifies(Collections.singletonList(identifyInfo));
            // 补充扩展字段
          /*  Map<String, Object> extrasMap = new HashMap<>();
            extrasMap.put("serviceShop", org.apache.commons.lang3.StringUtils.isBlank(event.getStr("serviceShop")) ? null : event.getStr("serviceShop"));
            extrasMap.put("oldMemberId", event.getStr("sysCustomerId"));
            extrasMap.put("enrollGuideName", org.apache.commons.lang3.StringUtils.isBlank(event.getStr("enrollGuideName")) ? null : event.getStr("enrollGuideName"));
            extrasMap.put("serviceGuideName", org.apache.commons.lang3.StringUtils.isBlank(event.getStr("serviceGuideName")) ? null : event.getStr("serviceGuideName"));
            createRequest.setExtras(extrasMap);
            createRequest.setEnrollTime(ZonedDateTime.parse(event.getStr("enrollTime")));*/
            try {
            //    log.info("微信历史会员memberId:{}创建参数:{}", memberId, JSON.toJSONString(createRequest));
                MemberCreateResult memberCreateResult = mbspClient.createMember(createRequest);
                log.info("微信历史会员memberId:{}创建返回结果:{}", memberId, JSON.toJSONString(memberCreateResult));
                if("success".equalsIgnoreCase(memberCreateResult.getMsg())){
                   /* Map<String, Object> map = new HashMap<>();
                    map.put("id", id);
                    map.put("isSuccess", "Y");
                    map.put("memberId", memberId);
                    dataapiHttpSdk.update(ModelTags.DATA_FQN_HIS_MEMBER_WECHAT, id, map, false);*/
                    String sql = String.format(
                            "update %s  set  isSuccess= 'Y',kylinMemberId='%s'  where id ='%s'",
                            event.getString("table"),memberCreateResult.getData().getMemberId(),id);
                    jdbcTemplate.update(sql);
                } else {
                   /* Map<String, Object> map = new HashMap<>();
                    map.put("id", id);
                    map.put("isSuccess", "F");
                    dataapiHttpSdk.update(ModelTags.DATA_FQN_HIS_MEMBER_WECHAT, id, map, false);*/
                    String sql = String.format(
                            "update %s  set  isSuccess= 'N' where id ='%s'",
                            event.getString("table"),id);
                    jdbcTemplate.update(sql);
                }
            } catch (Exception e) {
                log.warn("微信历史会员:{} 注册异常:{}", e.getMessage());
               /* Map<String, Object> map = new HashMap<>();
                map.put("id", id);
                map.put("isSuccess", "F");*/
                String sql = String.format(
                        "update %s  set  isSuccess= 'N'  where id ='%s'",
                        event.getString("table"),id);
                jdbcTemplate.update(sql);
                //dataapiHttpSdk.update(ModelTags.DATA_FQN_HIS_MEMBER_WECHAT, id, map, false);
            }
        }
    }

    public void registerHisMember(JSONObject param) {
        com.alibaba.fastjson.JSONObject event = (com.alibaba.fastjson.JSONObject) com.alibaba.fastjson.JSONObject.toJSON(param);
        long startTime = System.currentTimeMillis();
        String id = event.getString("id");
      //  log.info("开始处理微信/POS历史会员memberId:{} 信息:{}", event);
        String enrollChannel = event.getString("enrollChannel");
        String bizCode = event.getString("brand");
       // log.info("开始处理渠道:{}历史会员customerNo:{} 信息:{}", enrollChannel, event);
        RegisterRequest registerParam = JsonUtil.convert(event, RegisterRequest.class);
        registerParam.setProgramCode(bizCode);
        // 明文手机号加密
        List<String> mobileValues = new ArrayList<>();
        if(StringUtils.isEmpty(registerParam.getMobile())){
            mobileValues.addAll(Arrays.asList(registerParam.getMixMobile()));
        }else {
            mobileValues.add(registerParam.getMobile());
            mobileValues.addAll(mobileEncrypt(registerParam.getMobile(), bizCode));
        }
        MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
        identifyInfo.setCode("mobile");
        identifyInfo.setValues(mobileValues);
        registerParam.setIdentifies(Collections.singletonList(identifyInfo));
            //注册pos会员
            try {  log.info("渠道:{}历史会员customerNo:{} 注册参数:{}", enrollChannel, JSON.toJSONString(registerParam));
                RegisterResult registerResult = mbspClient.register(registerParam);
            log.info("渠道:{}历史会员customerNo:{}注册返回结果:{}", enrollChannel, JSON.toJSONString(registerResult));

                if("success".equalsIgnoreCase(registerResult.getMsg())){
                   // log.info("渠道:{}历史会员customerNo:{}注册成功,返回memberId:{}", enrollChannel, registerResult.getData().getMemberId());
                    String sql = String.format(
                            "update %s  set  isSuccess= 'Y',kylinMemberId='%s' ,lastSync=now() where id ='%s'",
                            event.getString("table"),registerResult.getData().getMemberId(),id);
                    jdbcTemplate.update(sql);
                } else {
                  //  log.warn("渠道:{}历史会员customerNo:{}注册失败!", enrollChannel);
                    String sql = String.format(
                            "update %s  set  isSuccess= 'N' ,lastSync=now() where id ='%s'",
                            event.getString("table"),id);
                    jdbcTemplate.update(sql);
                }
            } catch (Exception e) {
               // log.error("渠道:{}历史会员customerNo:{} 注册异常:{}", enrollChannel, e.getMessage());
                String sql = String.format(
                        "update %s  set  isSuccess= 'N' ,lastSync=now() where id ='%s'",
                        event.getString("table"),id);
                jdbcTemplate.update(sql);
            }

        long duration = System.currentTimeMillis() - startTime;
        log.info(" 注册历史会员:{}ms", duration);
    }



    public void unbindHisMember(JSONObject param) {
        com.alibaba.fastjson.JSONObject event = (com.alibaba.fastjson.JSONObject) com.alibaba.fastjson.JSONObject.toJSON(param);

        ChannelMemberUnbindRequest unbindRequest = JsonUtil.convert(event, ChannelMemberUnbindRequest.class);
        try {
            mbspClient.unbindChannelMember(unbindRequest);
        } catch (Exception e) {
            log.warn("解绑历史会员异常: {} ",e.getMessage());
        }
    }

    //会员卡券导入
    public BenefitBatchImportResponse importBatch(JSONObject param)  {
        com.alibaba.fastjson.JSONObject event = (com.alibaba.fastjson.JSONObject) com.alibaba.fastjson.JSONObject.toJSON(param);
        log.info("会员卡券导入开始:{}", param.get("memberId")+"-"+param.get("code"));
        BenefitBatchImportResponse benefitBatchImportResponse =new BenefitBatchImportResponse();
        try {
        long startTime = System.currentTimeMillis();
        Map<String, Object> convert = JsonUtil.convert(event, Map.class);
        BenefitBatchImportRequest request = new BenefitBatchImportRequest();
        request.setFqn(convert.get("fqn").toString());
        List<BenefitImportRequest> importRequests = new ArrayList<>();
        BenefitImportRequest benefitImportRequest = new BenefitImportRequest();
        Benefit benefit = new Benefit();
        benefit.setCode(convert.get("code").toString());
        benefit.setId(convert.get("code").toString());
        benefit.setExpiredManual(true);
        benefit.setEffectiveManual(true);
        benefit.setExtension("projectType",convert.get("projectType").toString());
        benefit.setExpiredAt(DateUtil.localTime((convert.get("expiredAt").toString())));
        benefit.setEffectiveAt(DateUtil.localTime((convert.get("effectiveAt").toString())));
        benefit.setActivateAt(DateUtil.localTime((convert.get("effectiveAt").toString())));
        benefit.setProgramId(convert.get("programId").toString());
        benefit.setProjectId(convert.get("projectId").toString());
        benefit.setTemplateId(convert.get("templateId").toString());
        benefit.setState(TicketState.EFFECTED);
        benefit.setGrantReason(convert.get("grantReason").toString());
        benefit.setGrantPlatform(convert.get("grantPlatform").toString());
        benefit.setHolder(convert.get("holder").toString());
        benefit.setCreateBy(convert.get("createBy").toString());
        benefit.setCreateAt(LocalDateTime.now());
        benefit.setUpdateBy(convert.get("updateBy").toString());
        benefit.setUpdateAt(LocalDateTime.now());
        benefit.setGrantAt(DateUtil.localTime((convert.get("effectiveAt").toString())));
        benefitImportRequest.setBenefit(benefit);
        benefitImportRequest.setTransactionId(benefit.getCode());
        importRequests.add(benefitImportRequest);
        request.setBenefitImports(importRequests);
        log.info("request : {}" , request);
            benefitService.importBatch(request);
            long duration = System.currentTimeMillis() - startTime;
            log.info(" 请求卡券导入{}成功:{}ms",param.get("memberId")+"-"+param.get("code"), duration);
        } catch (Exception e) {
            log.error("历史卡券导入失败",e);
            Map<String, Object> map = new HashMap<>();
            map.put("id", param.get("code"));
            map.put("couponCode", param.get("code"));
            map.put("lastSync", LocalDateTime.now());
            map.put("memberId", param.get("memberId"));
            dataapiHttpSdk.upsert("data.prctvmkt.CBANNER.hisErrorCoupon", param.get("couponCode").toString(), map, false);
        }
        return benefitBatchImportResponse;
    }
    private String getFqn(String enrollChannel){
        String fqn;
        if ("YOUZAN".equalsIgnoreCase(enrollChannel)){
            fqn = ModelTags.DATA_FQN_HIS_MEMBER_YOUZAN;
        } else if ("DOUYIN".equalsIgnoreCase(enrollChannel)){
            fqn = ModelTags.DATA_FQN_HIS_MEMBER_DOUYIN;
        } else if ("TAOBAO".equalsIgnoreCase(enrollChannel)){
            fqn = ModelTags.DATA_FQN_HIS_MEMBER_TAOBAO;
        } else if ("WECHAT".equalsIgnoreCase(enrollChannel)){
            fqn = ModelTags.DATA_FQN_HIS_MEMBER_WECHAT;
        } else {
            fqn = ModelTags.DATA_FQN_HIS_MEMBER_POS;
        }
        return fqn;
    }
}
