package com.shuyun.fast.service.v1_0_0;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.GradeCache;
import com.shuyun.fast.client.v1_0_0.LoyaltyFacadeClient;
import com.shuyun.fast.client.v1_0_0.LoyaltyManagerClient;
import com.shuyun.fast.config.GradeHierarchyConfiguration;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.loyalty.SortType;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.MemberNextGradeAmount;
import com.shuyun.fast.v1_0_0.param.grade.GradeGetParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeMergeParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeRecordsGetParam;
import com.shuyun.fast.v1_0_0.param.mdm.MdmGetAmountParam;
import com.shuyun.fast.v1_0_0.param.member.MemberGetNextAmountParam;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.loyalty.sdk.api.model.MemberMergeGradeRequest;
import com.shuyun.loyalty.sdk.api.model.OnlyMergeGradeRequest;
import com.shuyun.loyalty.sdk.api.model.Page;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeRecordResponse;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeResponse;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Singleton
@Slf4j
public class GradeService {
    private final LoyaltyManagerClient managerClient;
    private final LoyaltyFacadeClient facadeClient;
    private static final String tenantId = GlobalContext.defTenantId();
    private final BizCacheService bizCacheService;
    private final GradeHierarchyConfiguration gradeHierarchyConfiguration;

    public GradeService(LoyaltyManagerClient managerClient,
                        LoyaltyFacadeClient facadeClient,
                        BizCacheService bizCacheService,
                        GradeHierarchyConfiguration gradeHierarchyConfiguration) {
        this.managerClient = managerClient;
        this.facadeClient = facadeClient;
        this.bizCacheService = bizCacheService;
        this.gradeHierarchyConfiguration = gradeHierarchyConfiguration;
    }

    public GradeCache bizCacheGet(ApiBaseParam param, String gradeBizType) {
        GradeCache cache = bizCacheService.get(GradeCache.class, tenantId, param.getBizCode(), BizCache.GRADE)
                .stream()
                .filter(c -> gradeBizType.equals(c.getGradeBizType()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(cache)) {
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.GRADE);
        }
        return cache;
    }

    public GradeCache bizCacheGet(String bizCode, String gradeBizType) {
        GradeCache cache = bizCacheService.get(GradeCache.class, tenantId, bizCode, BizCache.GRADE)
                .stream()
                .filter(c -> gradeBizType.equals(c.getGradeBizType()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(cache)) {
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.GRADE);
        }
        return cache;
    }

    public MemberGradeResponse gradeGet(GradeGetParam param) {
        String memberId = param.getIdentify().getMemberId();
        String gradeBizType = param.getGradeBizType();
        GradeCache cache = bizCacheGet(param, gradeBizType);
        MemberGradeResponse defaultGrade = new MemberGradeResponse();
        defaultGrade.setCurrentGradeDefinitionId(cache.getGradeId());
        defaultGrade.setGradeDefinitionName(cache.getGradeName());
        return managerClient.findMemberGradeList(0, 20, cache.getGradeHierarchyid(), memberId, null)
                .stream()
                .findFirst()
                .orElse(defaultGrade);
    }

    public Page<MemberGradeRecordResponse> gradeRecordsGet(GradeRecordsGetParam param) {
        String memberId = param.getIdentify().getMemberId();
        String gradeBizType = param.getGradeBizType();
        GradeCache cache = bizCacheGet(param, gradeBizType);
        SortType sortType = "ASC".equals(param.getSortType()) ? SortType.CREATED_ASC : SortType.CREATED_DESC;
        return managerClient.pageMemberGradeRecordList(param.getPage(),
                param.getPageSize(),
                cache.getGradeHierarchyid(),
                memberId,
                null,
                sortType,
                null,
                null,
                null,
                DateUtil.utcTime(param.getStartTime()),
                DateUtil.utcTime(param.getEndTime()));
    }

    public void mergeGrade(GradeMergeParam param) {
        GradeCache cache = bizCacheGet(param.getBizCode(), param.getGradeBizType());
        OnlyMergeGradeRequest request = new OnlyMergeGradeRequest();
        request.setChannelType(param.getChannelType());
        request.setMemberId(param.getMainMemberId());
        request.setSubMemberIds(param.getMergeMemberIdList());
        request.setTriggerId(param.getTraceId());
        request.setDescription(param.getDescription());
        MemberMergeGradeRequest mergeGradeRequest = new MemberMergeGradeRequest();
        mergeGradeRequest.setGradeHierarchyIds(Arrays.asList(cache.getGradeHierarchyid()));
        mergeGradeRequest.setGradeMergeType(param.getGradeMergeType());
        mergeGradeRequest.setUseHighestEffectTimeType(param.getUseHighestEffectTimeType());
        request.setMergeGradeRequest(mergeGradeRequest);
        log.info("会员memberId:{}等级合并产品接口入参:{}", param.getMainMemberId(), JSONObject.toJSONString(request));
        facadeClient.mergeGrade(request);
    }

    public MemberNextGradeAmount nextGradeAmount(MemberGetNextAmountParam param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("nextGradeAmount param={}", JsonUtil.outPutSerialize(param));
        if (Objects.isNull(param.getGradeHierarchyId())) {
            param.setGradeHierarchyId(gradeHierarchyConfiguration.getHierarchyId());
        }
        //查询会员当前等级
        log.info("findMemberGradeList start..., memberId={}", param.getMemberId());
        List<MemberGradeResponse> responseList = managerClient.findMemberGradeList(0, 20, param.getGradeHierarchyId(), param.getMemberId(), null);
        if (CollUtil.isEmpty(responseList) || Objects.isNull(responseList.get(0).getCurrentGradeDefinitionId())) {
            log.error("get member current grade failed, memberId={}", param.getMemberId());
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "get member current grade failed");
        }
        log.info("findMemberGradeList end..., memberId={}", param.getMemberId());
        long currentGradeDefinitionId = responseList.get(0).getCurrentGradeDefinitionId();
        //等级规则写死
        List<Long> gradeList = Stream.of(gradeHierarchyConfiguration.getGradeDefinitionIdListStr().split(",")).map(Long::parseLong).sorted().collect(Collectors.toList());
        Long nextGradeDefinitionId = null;
        for (int i = 0; i < gradeList.size(); i++) {
            if (gradeList.get(i) == currentGradeDefinitionId) {
                if (i == gradeList.size() - 1) {
                    //已经是最高等级，则返回0
                    return new MemberNextGradeAmount("0", "明星会员");
                }
                nextGradeDefinitionId = gradeList.get(i + 1);
            }
        }
        if (Objects.isNull(nextGradeDefinitionId)) {
            log.error("get member next grade failed, memberId={}, currentGradeDefinitionId={}", param.getMemberId(), currentGradeDefinitionId);
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "get member next grade failed");
        }
        //查询累计消费金额
        MdmGetAmountParam mdmGetAmountParam = new MdmGetAmountParam();
        mdmGetAmountParam.setGradeHierarchyId(param.getGradeHierarchyId());
        mdmGetAmountParam.setMemberId(param.getMemberId());
        mdmGetAmountParam.setGradeRuleTypes(List.of("UPGRADE"));
        mdmGetAmountParam.setTargetGradeIds(List.of(nextGradeDefinitionId));
        log.info("budget start..., memberId={}", param.getMemberId());
        List<Map<String, Object>> budgetList = managerClient.budget(mdmGetAmountParam);
        log.info("budget end..., memberId={}", param.getMemberId());
        if (CollUtil.isEmpty(budgetList)) {
            log.error("get member budget failed, memberId={}, nextGradeDefinitionId={}", param.getMemberId(), nextGradeDefinitionId);
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "get member budget failed");
        }
        List<Map<String, Object>> gradeRuleList = (List) budgetList.get(0).get("gradeRuleList");
        if (CollUtil.isEmpty(gradeRuleList)) {
            log.error("get member budget failed, memberId={}, nextGradeDefinitionId={}", param.getMemberId(), nextGradeDefinitionId);
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "get member budget failed");
        }
        Map<String, Object> gradeRule = (Map) gradeRuleList.get(0).get("gradeRule");
        String gradeName = (String) gradeRule.get("gradeName");
        List<Map<String, Object>> filterGroupList = (List) gradeRule.get("filterGroupList");
        List<Map<String, Object>> filterList = (List) filterGroupList.get(0).get("filterList");
        BigDecimal nextGradeValue = Convert.toBigDecimal(filterList.get(0).get("value"));
        BigDecimal currentValue = Convert.toBigDecimal(filterList.get(0).get("currentValue"));
        log.info("nextGradeAmount end..., memberId={}, costMs={}", param.getMemberId(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        //用户消费金额已比下一等级大，未升级
        BigDecimal diff = nextGradeValue.subtract(currentValue);
        if (diff.compareTo(BigDecimal.ZERO) < 0) {
            diff = BigDecimal.ZERO;
        }
        return new MemberNextGradeAmount(diff.toPlainString(), gradeName);
    }
}
