package com.shuyun.fast.service.v1_0_0;
import com.shuyun.fast.annotation.BizCacheType;
import com.shuyun.fast.cache.v1_0_0.*;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.repository.BizCacheRepository;
import com.shuyun.ticket.util.JsonUtil;
import io.micronaut.cache.DefaultCacheManager;
import io.micronaut.cache.SyncCache;
import io.micronaut.cache.annotation.CacheInvalidate;
import io.micronaut.cache.annotation.CachePut;
import io.micronaut.cache.annotation.Cacheable;
import io.micronaut.core.beans.BeanIntrospection;
import io.micronaut.core.beans.BeanIntrospector;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Singleton
@Slf4j
public class BizCacheService {

    private final BizCacheRepository bizCacheRepository;
    private final SyncCache secondBizCache;//缓存刷新时防止击穿

    private Map<String, Class> cacheTypes = new ConcurrentHashMap();

    public BizCacheService(DefaultCacheManager cacheManager,
                           BizCacheRepository bizCacheRepository){
        this.bizCacheRepository = bizCacheRepository;
        this.secondBizCache = cacheManager.getCache("secondBizCache");
    }

    @PostConstruct
    public void init(){

        Collection<BeanIntrospection<Object>> types = BeanIntrospector.SHARED.findIntrospections(BizCacheType.class);
        types.forEach(t->{
            Class clazz = t.getBeanType();
            BizCacheType a = (BizCacheType)clazz.getDeclaredAnnotation(BizCacheType.class);
            String type = a.value();
            cacheTypes.put(type, clazz);
        });

        Iterable<BizCache> caches = bizCacheRepository.findAll();
        Iterator<BizCache> iterator = caches.iterator();
        while(iterator.hasNext()){
            BizCache cache = iterator.next();
            String tenantId = cache.getTenantId();
            String bizCode = cache.getBizCode();
            String cacheType = cache.getCacheType();
            Class<BaseCache> clazz = cacheTypes.get(cacheType);
            init(clazz, tenantId, bizCode, cacheType);
        }
        log.info("init biz cache success");
    }

    @CachePut(cacheNames = "firstBizCache", parameters = {"tenantId", "bizCode", "cacheType"})
    public <T extends BaseCache> List<T> init(Class<T> clazz, String tenantId, String bizCode, String cacheType){
        log.info("init cache from db,tenantId:{}...bizCode:{}...cacheType:{}", tenantId,bizCode,cacheType);
        String cacheContent;
        BizCache cache = bizCacheRepository.find(tenantId, bizCode, cacheType).orElse(null);
        log.info("cache:{}", JsonUtil.serialize(cache));
        if(Objects.nonNull(cache)){
            cacheContent = cache.getValue();
            return JsonUtil.listFromJson(cacheContent, clazz);
        }
        log.warn("find no {} cache by param tenantId:{} bizCode:{}", cacheType, tenantId, bizCode);
        return Collections.emptyList();
    }

    @Cacheable(cacheNames = "firstBizCache", parameters = {"tenantId", "bizCode", "cacheType"})
    public <T extends BaseCache> List<T> load(Class<T> clazz, String tenantId, String bizCode, String cacheType){
        log.info("load cache from db,tenantId:{}...bizCode:{}...cacheType:{}", tenantId,bizCode,cacheType);
        String cacheContent;
        BizCache cache = bizCacheRepository.find(tenantId, bizCode, cacheType).orElse(null);
        if(Objects.nonNull(cache)){
            cacheContent = cache.getValue();
            return JsonUtil.listFromJson(cacheContent, clazz);
        }
        return Collections.emptyList();
    }

    @CacheInvalidate(cacheNames = "firstBizCache", parameters = {"tenantId", "bizCode", "cacheType"})
    public <T extends BaseCache> void invalid(Class<T> clazz, String tenantId, String bizCode, String cacheType){

    }


    public <T extends BaseCache> List<T> get(String tenantId, String bizCode, String cacheType){
        Class clazz = cacheTypes.get(cacheType);
        return get(clazz, tenantId, bizCode, cacheType);
    }

    public <T extends BaseCache> List<T> get(Class<T> clazz, String tenantId, String bizCode, String cacheType){
        String key = String.join("-", tenantId, bizCode, cacheType);
        List<T> value = (List<T>)secondBizCache.get(key, List.class, ()-> Collections.emptyList());
        return value.isEmpty() ? load(clazz, tenantId, bizCode, cacheType):value;
    }

    /**
     * 1、备份数据到二级缓存
     * 2、失效一级缓存
     * 3、重载一级缓存
     * 4、失效二级缓存
     * @param tenantId
     * @param bizCode
     * @param cacheType
     * @param <T>
     */
    public <T extends BaseCache> void refresh(String tenantId, String bizCode, String cacheType){
        Class clazz = cacheTypes.get(cacheType);
        String key = String.join("-", tenantId, bizCode, cacheType);
        List<T> value = load(clazz, tenantId, bizCode, cacheType);
        secondBizCache.put(key, value);
        invalid(clazz, tenantId, bizCode, cacheType);
        List<T> cache = load(clazz, tenantId, bizCode, cacheType);
        if(!cache.isEmpty()){
            secondBizCache.invalidate(key);
        }
    }
}
