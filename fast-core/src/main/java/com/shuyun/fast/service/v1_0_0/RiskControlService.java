package com.shuyun.fast.service.v1_0_0;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.client.v1_0_0.RiskControlClient;
import com.shuyun.fast.config.RiskControlConfiguration;
import com.shuyun.fast.v1_0_0.domain.RiskControlCheckListRequest;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class RiskControlService {

    @Inject
    private RiskControlClient riskControlClient;
    @Inject
    private RiskControlConfiguration riskControlConfiguration;

    public void checkList(JSONObject event) {
        String memberId = event.getString("memberId");
        String enabled = event.getString("enabled");
        RiskControlCheckListRequest request = new RiskControlCheckListRequest();
        request.setChecklistType("BLACK");
        request.setCustomer(memberId);
        request.setGroupId(riskControlConfiguration.getEmployee());
        request.setFqn(ModelTags.DATA_FQN_MEMBER);
        if ("Y".equals(enabled)) {
            log.info("checklist add black list, memberId={}", memberId);
            riskControlClient.addBlackList(request);
        } else if ("N".equals(enabled)) {
            log.info("checklist remove black list, memberId={}", memberId);
            riskControlClient.removeBlackList(request);
        } else {
            log.error("checklist invalid param[enabled], memberId={}", memberId);
        }
    }
}
