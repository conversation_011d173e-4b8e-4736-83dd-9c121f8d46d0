package com.shuyun.fast.service.v1_0_0;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.util.Md5;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Singleton
public class RFMService {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
    final static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    public void calculate(JSONObject event) {
        String memberId = event.getString("memberId");
        String memberType = event.getString("memberType");
        JSONObject member = event.getJSONObject("member");
//        String type = event.getString("type"); //事件类型 Order RefundOrder

        //累计消费金额
        BigDecimal sumPayment = BigDecimal.ZERO;
        //累计消费次数
        int sumOrder = 0;
        //最后消费日期
        ZonedDateTime lastOrderTime = null;
        //最后消费店铺code
        String lastOrderShopCode = null;
        //最后消费店铺名称
        String lastOrderShopName = null;
        //近一年累计实付金额
        BigDecimal yearPayment = BigDecimal.ZERO;
        //一年前utc时间
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        ZonedDateTime oneYearAgo = now.minusYears(1);

        //累计订单
        String sumPaymentOrderSql = String.format("select payment,orderTime,shopCode,shopName from %s where orderType='NORMAL' and orderStatus='FINISHED' and memberId='%s'", ModelTags.DATA_FQN_MAIN_ORDER, memberId);
        BaseResponse<Map<String, Object>> sumPaymentOrderExecute = dataapiHttpSdk.execute(sumPaymentOrderSql, Collections.emptyMap());
        if (CollUtil.isNotEmpty(sumPaymentOrderExecute.getData())) {
            sumOrder = sumPaymentOrderExecute.getData().size();
            for (Map<String, Object> map : sumPaymentOrderExecute.getData()) {
                sumPayment = sumPayment.add((BigDecimal) map.get("payment"));
                //时间格式2024-12-26T01:36:10.000Z，UTC时间
                String orderTimeStr = (String) map.get("orderTime");
                ZonedDateTime orderTime = ZonedDateTime.parse(orderTimeStr);
                String shopCode = (String) map.get("shopCode");
                String shopName = (String) map.get("shopName");
                if (Objects.isNull(lastOrderTime) || lastOrderTime.isBefore(orderTime)) {
                    lastOrderTime = orderTime;
                    lastOrderShopCode = shopCode;
                    lastOrderShopName = shopName;
                }
                if (oneYearAgo.toInstant().equals(orderTime.toInstant()) ||
                        oneYearAgo.toInstant().isBefore(orderTime.toInstant())) {
                    //一年内包括边界
                    yearPayment = yearPayment.add((BigDecimal) map.get("payment"));
                }
            }
        }
        //累计退单
        String sumPaymentRefundOrderSql = String.format("select payment,refundTime from %s where orderType='REFUND' and orderStatus='REFUND_FINISHED' and memberId='%s' and originOrderId in (select orderId from %s where orderType='NORMAL' and orderStatus='FINISHED' and memberId='%s')", ModelTags.DATA_FQN_MAIN_ORDER, memberId, ModelTags.DATA_FQN_MAIN_ORDER, memberId);
        BaseResponse<Map<String, Object>> sumPaymentRefundOrderExecute = dataapiHttpSdk.execute(sumPaymentRefundOrderSql, Collections.emptyMap());
        if (CollUtil.isNotEmpty(sumPaymentRefundOrderExecute.getData())) {
            for (Map<String, Object> map : sumPaymentRefundOrderExecute.getData()) {
                sumPayment = sumPayment.add((BigDecimal) map.get("payment"));
                String refundTimeStr = (String) map.get("refundTime");
                ZonedDateTime refundTime = ZonedDateTime.parse(refundTimeStr);
                if (oneYearAgo.toInstant().equals(refundTime.toInstant()) ||
                        oneYearAgo.toInstant().isBefore(refundTime.toInstant())) {
                    //一年内包括边界
                    yearPayment = yearPayment.add((BigDecimal) map.get("payment"));
                }
            }
        }
        //新增/更新
        Map<String, Object> map = new HashMap<>();
        map.put("brand", memberType);
        map.put("memberId", memberId);
        map.put("sumPayment", sumPayment);
        map.put("sumOrder", sumOrder);
        map.put("lastOrdertime", lastOrderTime == null ? null : lastOrderTime.format(formatter));
        map.put("lastOrderShopCode", lastOrderShopCode);
        map.put("lastOrderShopName", lastOrderShopName);
        map.put("yearPayment", yearPayment);
        map.put("lastSync", now.format(formatter));
        map.put("member", member);
        log.info("calculate result = {}", JsonUtil.outPutSerialize(map));
        dataapiHttpSdk.upsert(ModelTags.DATA_FQN_MEMBER_RFM, Md5.encryption(memberId), map, false);
    }
}
