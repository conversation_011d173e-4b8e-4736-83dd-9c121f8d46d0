package com.shuyun.fast.service.v1_0_0;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.client.v1_0_0.EbrandMemberClient;
import com.shuyun.fast.client.v1_0_0.LoyaltyFacadeClient;
import com.shuyun.fast.client.v1_0_0.LoyaltyManagerClient;
import com.shuyun.fast.client.v1_0_0.MbspClient;
import com.shuyun.fast.douyin.param.DoudianMemberInfo;
import com.shuyun.fast.douyin.param.DoudianSyncRequest;
import com.shuyun.fast.douyin.result.EbrandApiResponse;
import com.shuyun.fast.taobao.param.SyncRequest;
import com.shuyun.fast.taobao.result.SyncResponse;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.v1_0_0.domain.Point;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeResponse;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author:luoruizhe
 * @Date:2024/8/22 15:29
 */
@Singleton
@Slf4j
public class EbrandMemberSyncService {

    private final EbrandMemberClient ebrandClient;


    private final MbspClient mbspClient;

    private final LoyaltyFacadeClient facadeClient;

    private final LoyaltyManagerClient managerClient;


    public EbrandMemberSyncService(EbrandMemberClient ebrandClient, MbspClient mbspClient, LoyaltyFacadeClient facadeClient, LoyaltyManagerClient managerClient) {
        this.ebrandClient = ebrandClient;
        this.mbspClient = mbspClient;
        this.facadeClient = facadeClient;
        this.managerClient = managerClient;
    }

    //淘宝会员信息同步

    //{"nick":"24KVNRfMr-EOJC_XwuRMISENCtaKg","point":755,"level":60008,
    // "seller_name":"珀莱雅官方旗舰店","mix_mobile":"b98be8822cbc0422bed2d45982cb5812"}
    public void taobaoEbrandSync(String param) {
        long startTime = System.currentTimeMillis();
        JSONObject entries = JSONUtil.parseObj(param);
        log.info("淘宝会员当前入参 To syncDouYin{}", entries);
        String memberId = entries.getStr("memberId");
        //最低等级
        Long gradeId = Long.valueOf(entries.getStr("gradeId"));

        Long pointAccountTypeId = Long.valueOf(entries.getStr("pointAccountTypeId"));
        Long gradeHierarchyid = Long.valueOf(entries.getStr("gradeHierarchyid"));
        String nick = entries.getStr("nick");
        String ouid = entries.getStr("ouid");

        SyncRequest request = new SyncRequest();
        request.setNick(nick);
        request.setOuid(ouid);
        Point point = facadeClient.queryPoint(memberId, pointAccountTypeId);
        request.setPoint(point.getPoint().longValue());
        MemberGradeResponse defaultGrade = new MemberGradeResponse();
        defaultGrade.setCurrentGradeDefinitionId(gradeId);
        defaultGrade = managerClient.findMemberGradeList(0, 20, gradeHierarchyid, memberId, null)
                .stream()
                .findFirst()
                .orElse(defaultGrade);
        log.info("淘宝会员 To taobao等级{}", defaultGrade);
        request.setLevel(defaultGrade.getCurrentGradeDefinitionId());
        request.setSellerName(entries.getStr("sellerName"));
        request.setMixMobile(entries.getStr("mixMobile"));

        log.info("淘宝会员当前信息 To syncTAOBAO{}", request);

        SyncResponse syncResponse = ebrandClient.taobaoSync(request);
        if (syncResponse.isSuccess()) {     // 成功
            log.info("[TaoBao] sync success! ");
        } else if (syncResponse.getMsg().equalsIgnoreCase("success")) {
            // {"success":false,"nick":"24KPVDABZiuWKRWlBfNBXw3SGo9AA","msg":"success"}   此版本会员通 这种情况也属于成功，测试已同步到平台
            log.info("[TaoBao] sync [msg] success! ");
        } else {    // 失败
            String id =memberId+entries.getStr("shopCode");
            log.error("[TaoBao] Error : " + syncResponse.getMsg());
            Map<String,Object> entries1 = JSONUtil.parseObj(request);
            entries1.put("sellerName",entries.getStr("sellerName"));
            entries1.put("mixMobile",entries.getStr("mixMobile"));
            entries1.put("memberId",defaultGrade.getCurrentGradeDefinitionId());
            entries1.put("id",id);
            entries1.put("customerNo",syncResponse.getMsg());
            entries1.put("lastSync", LocalDateTime.now());
            entries1.put("shopCode", entries.getStr("shopCode"));
            log.error("[TaoBao] entries1 : " + entries1);
 }
        long duration = System.currentTimeMillis() - startTime;
        log.info("淘宝会员同步 执行时间:{}ms", duration);
    }

    //抖音会员信息同步
    public void douyinEbrandSync(String param) {
        long startTime = System.currentTimeMillis();
        JSONObject entries = JSONUtil.parseObj(param);
        //log.info("抖音会员当前入参 To syncDouYin{}", entries);
        String memberId = entries.getStr("memberId");

        Long gradeId = Long.valueOf(entries.getStr("gradeId"));
        Long pointAccountTypeId = Long.valueOf(entries.getStr("pointAccountTypeId"));
        Long gradeHierarchyid = Long.valueOf(entries.getStr("gradeHierarchyid"));

        //request:{"app_id":1,"member_info_list":[{"mobile":"***********","open_id":"1@#w88vm+ANGrfOBuOBEJazpakkBv+kfi6D4EvYtIOQkBzHAH5JSnTdwX7P3fHvrw00FZE=",
        // "integral":778,"level":60008,"unbind":false,"shopCode":"3123593",
        // "pointCent":77800,"union_id":"1@#082tXW+B88awARy93t7erSUSbnMo4WPndNI6e9RupxVY3sSaSdEr2+FnqtFu7cfKs5yb/hbT3e00QigfWHJoUQ=="}]}
        Point point = facadeClient.queryPoint(memberId, pointAccountTypeId);
        //查看当前积分，等级
        String openId = entries.getStr("openId");
        String shopCode = entries.getStr("shopCode");


        DoudianMemberInfo doudianMember = new DoudianMemberInfo();
        doudianMember.setOpen_id(openId);
        doudianMember.setMobile(entries.getStr("mobile"));

        doudianMember.setShopCode(shopCode);
        doudianMember.setPointCent(point.getPoint().longValue() * 100);
        MemberGradeResponse defaultGrade = new MemberGradeResponse();
        defaultGrade.setCurrentGradeDefinitionId(gradeId);


        defaultGrade = managerClient.findMemberGradeList(0, 20, gradeHierarchyid, memberId, null)
                .stream()
                .findFirst()
                .orElse(defaultGrade);
        //log.info("抖音会员 To DouYin等级{}", defaultGrade);
        doudianMember.setLevel(defaultGrade.getCurrentGradeDefinitionId().intValue());
       // log.info("抖音会员当前信息 To syncDouYin{}", defaultGrade);
        if (!ObjectUtils.isEmpty(entries.getStr("unionId"))) {
            doudianMember.setUnion_id(entries.getStr("unionId"));
        }
        DoudianSyncRequest request = new DoudianSyncRequest();
        List<DoudianMemberInfo> list = new ArrayList<>();
        list.add(doudianMember);
        request.setMember_info_list(list);
          EbrandApiResponse ebrandApiResponse = ebrandClient.douyinSync(request);

        if(ebrandApiResponse.getError_code() == null
                || ebrandApiResponse.getError_code().trim().equals("")) {  // 成功
            log.info("[DouYin] sync success! ");
        } else {    // 失败
            log.error("[DouYin] Error : " + ebrandApiResponse.getMsg());
            String id =memberId+entries.getStr("shopCode");
            Map<String,Object> entries1 = JSONUtil.parseObj(request);
            entries1.put("mobile",entries.getStr("mobile"));
            entries1.put("memberId",defaultGrade.getCurrentGradeDefinitionId());
            entries1.put("id",id);
            entries1.put("customerNo",openId);
            entries1.put("lastSync", LocalDateTime.now());
            entries1.put("shopCode", entries.getStr("shopCode"));
            log.error("[DouYin] entries1 : " + entries1);
        }
        long duration = System.currentTimeMillis() - startTime;
        log.info("抖音会员同步 执行时间:{}ms", duration);
    }

}
