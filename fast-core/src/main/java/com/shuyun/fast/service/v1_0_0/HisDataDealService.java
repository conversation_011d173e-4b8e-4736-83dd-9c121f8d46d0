package com.shuyun.fast.service.v1_0_0;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.client.v1_0_0.GuideClient;
import com.shuyun.fast.client.v1_0_0.LoyaltyFacadeClient;
import com.shuyun.fast.client.v1_0_0.LoyaltyManagerClient;
import com.shuyun.fast.client.v1_0_0.MbspClient;
import com.shuyun.fast.factory.ThreadPoolExecutorFactory;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.util.SignConvertUtil;
import com.shuyun.fast.v1_0_0.param.guide.GuideJobAndJurisdictionShopsParam;
import com.shuyun.fast.v1_0_0.param.history.PrepareRequest;
import com.shuyun.fast.v1_0_0.result.KylinGuideResponse;
import com.shuyun.kylin.member.api.request.BindRequest;
import com.shuyun.kylin.member.api.request.MemberCreateRequest;
import com.shuyun.kylin.member.api.request.MemberQueryRequest;
import com.shuyun.kylin.member.api.request.RegisterRequest;
import com.shuyun.kylin.member.api.response.MemberCreateResult;
import com.shuyun.kylin.member.api.response.MemberQueryResult;
import com.shuyun.kylin.member.api.response.RegisterResult;
import com.shuyun.kylin.security.utils.KylinUtil;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeModifyRequest;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointDeductRequest;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointImportRequest;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointSendRequest;
import com.shuyun.pip.util.ObjectUtils;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @title HisDataDealService
 * @description 历史数据处理
 * @create 2024/10/11 10:21
 */

@Singleton
@Slf4j
public class HisDataDealService {

    @Inject
    private MbspClient mbspClient;

    @Inject
    private LoyaltyFacadeClient loyaltyFacadeClient;

    @Inject
    private GuideClient guideClient;

    @Inject
    private LoyaltyManagerClient loyaltyManagerClient;
    private final JdbcTemplate jdbcTemplate;

    private ThreadPoolExecutor executePoolExecutor = null;

    //    private final String url ="http://api.wg2.shuyun.com/openapi-server/v1/member/bindGuide";
    private final String url ="http://api-l7x23b9f5m.kylin.shuyun.com/openapi-server/v1";



    @PostConstruct
    public void init() {
        executePoolExecutor = ThreadPoolExecutorFactory.get(16, 32, 32, this.getClass().getName());
        executePoolExecutor.prestartAllCoreThreads();
        log.info("历史积分明细导入处理线程池初始化完毕>>>>>>>>>>>>>>");
    }
    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
    public HisDataDealService(JdbcTemplate jdbcTemplate,LoyaltyFacadeClient loyaltyFacadeClient,LoyaltyManagerClient loyaltyManagerClient,GuideClient guideClient) {
        this.jdbcTemplate = jdbcTemplate;
        this.loyaltyFacadeClient =  loyaltyFacadeClient;
        this.loyaltyManagerClient = loyaltyManagerClient;
        this.guideClient = guideClient;
    }

    public void ouidDeal(JSONObject param) throws Exception {
        String id = param.getString("id");
        String ouid = param.getString("ouid");
        String tableName = param.getString("tableName");
        String kylinId = KylinUtil.encrypt(ouid,"o0FH8H7lk3ORbLtm");
        String sql;
        if (StringUtils.isNotBlank(param.getString("omid"))) {
            String kylinOmid = KylinUtil.encrypt(param.getString("omid"),"o0FH8H7lk3ORbLtm");
            sql = "update "+tableName+" set kylinId = '"+kylinId+"', kylinOmid = '"+kylinOmid+"' where id = '"+id+"'";
        } else {
            sql = "update "+tableName+" set kylinId = '"+kylinId+"' where id = '"+id+"'";
        }
        log.info("执行SQL:{}", sql);
        jdbcTemplate.execute(sql);
    }

    public void hisMemberRegister(JSONObject param) {
        JSONObject event = (JSONObject) JSONObject.toJSON(param);
        log.info("fast.event.history.member.deal:{}",event);
        String tablaName = event.get("tableName").toString();
        Integer id = event.getInteger("id");
        String memberId = event.getString("memberId");

        if(StringUtils.isBlank(memberId)){
            RegisterRequest requestParam = JsonUtil.convert(event,RegisterRequest.class);
            RegisterResult result = mbspClient.register(requestParam);
        }else{
            MemberQueryRequest  quryRequest = new MemberQueryRequest();
            quryRequest.setMemberId(memberId);
            quryRequest.setProgramCode(param.getString("programCode"));
            MemberQueryResult queryResult = mbspClient.query(quryRequest);
            if(null != queryResult && null != queryResult.getData()){
                MemberCreateRequest createRequest = JsonUtil.convert(param,MemberCreateRequest.class);
                MemberCreateResult createResult = mbspClient.createMember(createRequest);
            }else{
                BindRequest bindRequest = JsonUtil.convert(param,BindRequest.class);
                mbspClient.bind(bindRequest);
            }
        }
       /* if(StringUtils.isEmpty(result.getError_code())){
            memberId = result.getData().getMemberId();
            String updateSql = "update "+tablaName+" set isRegister = 1,kylinMemberId='"+memberId+"' where id="+id+"";
            jdbcTemplate.execute(updateSql);
        }*/
    }


    public void hisGradeDeal(JSONObject param) {
        String tableName = param.get("tableName")==null?null:param.getString("tableName");
        Integer id = param.get("id")==null?null:param.getInteger("id");
        MemberGradeModifyRequest request = new MemberGradeModifyRequest();
        request.setMemberId(param.getString("memberId"));
        request.setChannelType(param.getString("channelType"));
        request.setDescription(param.getString("desc"));
        request.setTriggerId(param.getString("key"));
        request.setGradeDefinitionId(param.getLong("currentGradeDefinitionId"));
        request.setGradeHierarchyId(param.getLong("gradeHierarchyId"));
        ZonedDateTime overdueDate = ObjectUtils.isEmpty(param.get("overdueDate")) ? null : DateUtil.mbspInputTimeConvert(param.get("overdueDate").toString().substring(0, 19).replace("T", " "), "yyyy-MM-dd HH:mm:ss");
        request.setOverdueDate(overdueDate);
        loyaltyFacadeClient.gradeModify(request);
        if(StringUtils.isNotBlank(tableName)) {
            String sql = "update " + tableName + " set isDeal = 1 where id=" + id + "";
            jdbcTemplate.execute(sql);
        }
    }

    public void hisPointSend(JSONObject param) {
        String tableName = param.get("tableName")==null?null:param.getString("tableName");
        Integer id = param.get("id")==null?null:param.getInteger("id");
        MemberPointSendRequest request = new MemberPointSendRequest();
        request.setUniqueId(param.getString("key"));
        request.setPointAccountId(param.getLong("pointAccountId"));
        request.setMemberId(param.getString("memberId"));
        request.setChannelType(param.getString("channelType"));
        request.setDesc(param.getString("desc"));
        request.setTriggerId(param.getString("key"));
        request.setPoint(param.getBigDecimal("point"));
        if(!ObjectUtils.isEmpty(param.get("description"))){
            request.setDesc(param.getString("description"));
        }
        ZonedDateTime overdueDate = ObjectUtils.isEmpty(param.get("overdueDate")) ? null : DateUtil.mbspInputTimeConvert(param.get("overdueDate").toString().substring(0, 19).replace("T", " "), "yyyy-MM-dd HH:mm:ss");
        request.setOverdueDate(overdueDate);
        loyaltyFacadeClient.send(request);
        if(StringUtils.isNotBlank(tableName)){
            String sql = "update "+tableName+" set isDeal = 1 where id="+id+"";
            jdbcTemplate.execute(sql);
        }
    }
    public void hisPointDeduct(JSONObject param) {
        String tableName = param.get("tableName")==null?null:param.getString("tableName");
        Integer id = param.get("id")==null?null:param.getInteger("id");
        MemberPointDeductRequest request = new MemberPointDeductRequest();
        request.setUniqueId(param.getString("key"));
        request.setPointAccountId(param.getLong("pointAccountId"));
        request.setMemberId(param.getString("memberId"));
        request.setChannelType(param.getString("channelType"));
        request.setDesc(param.getString("desc"));
        request.setTriggerId(param.getString("key"));
        request.setPoint(param.getBigDecimal("point"));
        if(!ObjectUtils.isEmpty(param.get("description"))){
            request.setDesc(param.getString("description"));
        }
        //ZonedDateTime overdueDate = ObjectUtils.isEmpty(param.get("overdueDate")) ? null : DateUtil.mbspInputTimeConvert(param.get("overdueDate").toString().substring(0, 19).replace("T", " "), "yyyy-MM-dd HH:mm:ss");
        loyaltyFacadeClient.deduct(request);
        if(StringUtils.isNotBlank(tableName)) {
            String sql = "update " + tableName + " set isDeal = 1 where id=" + id + "";
            jdbcTemplate.execute(sql);
        }
    }


    public void hisMemberGuidePush(JSONObject param,String requestPath ) {
        log.info("导购开始请求:{}", param.get("memberId")+"-"+param.get("employeeId"));
        long startTime = System.currentTimeMillis();
        try {
            String serviceName = "fast-service";
            String contextPath = "openapi-server";
            String serviceSecret = "gdis22kslllk2";
            String version = "v1";
            String time = DateUtil.currentTime();
            String sign = SignConvertUtil.generateSign(serviceName, contextPath, version, time, serviceSecret, requestPath);
            HttpRequest httpRequest = HttpRequest.post(url + requestPath).body(JSONObject.toJSONString(param));
            httpRequest.header("Content-Type", "application/json;charset=UTF-8");
            httpRequest.header("X-Caller-Service", serviceName);
            httpRequest.header("X-Caller-Timestamp", time);
            httpRequest.header("X-Caller-Sign", sign);
           /* httpRequest.header("tenantsource", "KYLIN");
            httpRequest.header("membertype", "CBANNER");
            httpRequest.header("tenantid", "p5k0t4png0");*/
            HttpResponse execute = httpRequest.execute();
            log.info("导购接口返回:{}", execute.body());
            int status = execute.getStatus();
            if (200 == status) {
                log.info("请求导购成功");
            }
        }catch (Exception e){
            log.error("历史会员绑定关系推送导购出现异常",e);
            Map<String, Object> map = new HashMap<>();
            map.put("id", param.get("memberId"));
            map.put("memberId", param.get("memberId"));
            dataapiHttpSdk.upsert("data.prctvmkt.CBANNER.hisErrorMember", param.get("memberId").toString(), map, false);
        }
        long duration = System.currentTimeMillis() - startTime;
        log.info(" 请求导购{}成功:{}ms",param.get("memberId")+"-"+param.get("employeeId"), duration);

    }

    public void hisMemberOrderGuidePush(String orderType, String orderId, JSONObject param) {
        String requestPath = "/order/batchReceive4KyLin";
        long startTime = System.currentTimeMillis();
        Map<String, Object> map = new HashMap<>();
        map.put("id", DigestUtil.md5Hex(orderId));
        map.put("orderId", orderId);
        map.put("lastSync", LocalDateTime.now());
        String fqn = "NORMAL".equalsIgnoreCase(orderType) ? ModelTags.DATA_FQN_GUIDE_MEMBER_ORDER :ModelTags.DATA_FQN_GUIDE_MEMBER_REFUND;
        try {
            String serviceName = "fast-service";
            String contextPath = "openapi-server";
            String serviceSecret = "gdis22kslllk2";
            String version = "v1";
            String time = DateUtil.currentTime();
            String sign = SignConvertUtil.generateSign(serviceName, contextPath, version, time, serviceSecret, requestPath);
            HttpRequest httpRequest = HttpRequest.post(url + requestPath).body(JSONObject.toJSONString(param));
            httpRequest.header("Content-Type", "application/json;charset=UTF-8");
            httpRequest.header("X-Caller-Service", serviceName);
            httpRequest.header("X-Caller-Timestamp", time);
            httpRequest.header("X-Caller-Sign", sign);
            log.info("开始推送导购会员订单:{}", orderId);
            long start = System.currentTimeMillis();
            HttpResponse execute = httpRequest.execute();
            log.info("推送导购会员订单:{} 结束,导购订单接口响应时间:{}ms,接口返回:{}", orderId, System.currentTimeMillis() -start, execute.body());
            int status = execute.getStatus();
            if (status >= 200 && status < 300) {
                map.put("isSuccess", "Y");
                log.info("会员订单:{}推送导购成功!", orderId);
            }else{
                map.put("isSuccess", "N");
                log.warn("会员订单:{}推送导购失败!", orderId);
            }
            dataapiHttpSdk.upsert(fqn, DigestUtil.md5Hex(orderId), map, false);
        }catch (Exception e){
            log.warn("会员订单:{}推送导购异常:{}", orderId, e.getMessage());
            map.put("isSuccess", "N");
            dataapiHttpSdk.upsert(fqn, DigestUtil.md5Hex(orderId), map, false);
        }
        long duration = System.currentTimeMillis() - startTime;
        log.info(" 会员订单:{}推送导购成功:{}ms", orderId, duration);

    }


    public static void main(String[] args) {
        JSONObject param = new JSONObject();

        param.put("orderId","RE2411260000093");
        param.put("channelType","POS");

        param.put("shopCode","DVFQDHS");
        param.put("shopName","DVF青岛海信店");
        param.put("shopTypeCode","正价店铺");
        param.put("guideCode","QDHS19");
        param.put("orderStatus","FINISHED");
        param.put("orderType","NORMAL");
        param.put("payTime","2024-11-26 18:00:25");
        param.put("payment",8680.00);
        param.put("totalFee",8680.00);
        param.put("totalQuantity",8680.00);
        List<Object> objects = new ArrayList<>();
        objects.add(param);
        JSONObject params = new JSONObject();
        params.put("list",objects);

        System.out.println(param);
        System.out.println(params);
        /*String url ="http://api.wg2.shuyun.com/openapi-server/v1/order/batchReceive4KyLin";
            String serviceName = "15448592260735696905";
            String contextPath = "openapi-server";
            String serviceSecret = "7d7bca026f1811ef94fb98039bc0e2cc";
            String version = "v1";
            String time = DateUtil.currentTime();
            String sign = SignConvertUtil.generateSign(serviceName, contextPath, version, time, serviceSecret, "/order/batchReceive4KyLin");
            HttpRequest httpRequest = HttpRequest.post(url).body(JSONObject.toJSONString(param));
            httpRequest.header("Content-Type", "application/json;charset=UTF-8");
            httpRequest.header("X-Caller-Service", serviceName);
            httpRequest.header("X-Caller-Timestamp", time);
            httpRequest.header("X-Caller-Sign", sign);
            httpRequest.header("tenantsource", "KYLIN");
            httpRequest.header("membertype", "DVF");
            httpRequest.header("tenantid", "p5k0t4png0");
            HttpResponse execute = httpRequest.execute();
            log.info("导购接口返回:{}", execute.body());
            int status = execute.getStatus();
            if (200 == status) {
                log.info("请求导购成功");
            }*/

    }

    public void pointImport(PrepareRequest request) throws Exception{
        final long limit = request.getLimit();
        String tableName = request.getTableName();
        String type = request.getType();
        String migrationId = request.getMigrationId();
        long planId  = request.getPlanId();
        long pointId  = request.getPointId();
        boolean overrideHistoryPoint = request.isOverrideHistoryPoint();
        new Thread(()->{
            for(long i=request.getStartPage();i<=request.getEndPage();i++) {
                final long page = i;
                final long start = page * limit;
                final long end = start+limit;
                try{
                    executePoolExecutor.getQueue().put(() -> {
                        try{
                            List<Map<String,Object>> hisDataList = queryHisPoint(tableName,start,end);
                            List<MemberPointImportRequest> pointList = new ArrayList<MemberPointImportRequest>();
                            log.info("忠诚度积分明细导入批次条数：{}",hisDataList.size());
                            if(hisDataList.size()==0){
                                return;
                            }else{
                                hisDataList.forEach(map->{
                                    MemberPointImportRequest parms = new MemberPointImportRequest();
                                    parms.setMemberId(map.get("memberId").toString());
                                    parms.setAction(map.get("action").toString());
                                    parms.setEffectiveDate(ZonedDateTime.parse(map.get("effectiveDate").toString()));
                                    if(!ObjectUtils.isEmpty(map.get("overdueDate"))){
                                        parms.setOverdueDate(ZonedDateTime.parse(map.get("overdueDate").toString()));
                                    }
                                    parms.setPoint(BigDecimal.valueOf(Integer.valueOf(map.get("point").toString())));
                                    parms.setDescription(map.get("description").toString());
                                    parms.setKey(map.get("key").toString());
                                    parms.setChannelType(map.get("channelType").toString());
                                    parms.setCreatedDate(ZonedDateTime.parse(map.get("createdDate").toString()));
                                    if(!ObjectUtils.isEmpty(map.get("shopId"))) {
                                        parms.setShopId(map.get("shopId").toString());
                                    }
                                    pointList.add(parms);
                                });
                            }
                            log.debug("忠诚度积分明细导入入参：{}",JSONObject.toJSON(pointList));
                            loyaltyFacadeClient.saveImportRecord(migrationId,planId,pointId,overrideHistoryPoint,pointList);
                        }catch (Exception e2){
                            log.error("e2:",e2);
                        }

                    });
                }catch(Exception e){
                    log.error("error1：{}",e);
                }
            }
        }).start();
    }

    public void pointUse(PrepareRequest request) throws Exception{
        String migrationId = request.getMigrationId();
        long planId  = request.getPlanId();
        long pointId  = request.getPointId();
        loyaltyFacadeClient.applyImportRecord(migrationId,pointId);
    }
    public List<Map<String,Object>> queryHisPoint(String tableName,long start ,long end){
        String sql = "select memberId,`action`,channelType,`point`,DATE_FORMAT(date_sub(effectiveDate,interval 8 HOUR),'%Y-%m-%dT%H:%i:%sZ') as effectiveDate,DATE_FORMAT(date_sub(overdueDate,interval 8 HOUR),'%Y-%m-%dT%H:%i:%sZ') as overdueDate,DATE_FORMAT(date_sub(createdDate,interval 8 HOUR),'%Y-%m-%dT%H:%i:%sZ') as createdDate,`key`,description,shopId from "+tableName+" where id >"+start+" and id <="+end+" and isSend=0 and point >0";
        log.debug("querySql:{}",sql);
        return jdbcTemplate.queryForList(sql);
    }

    public void guideExternalChange(Map<String,Object> param) {
        try{
            /*GuideJobAndJurisdictionShopsParam request = new GuideJobAndJurisdictionShopsParam();
            if(null != param.get("areaCodes")){
                request.setAreaCodes((List<String>)param.get("areaCodes"));
            }
            if(null != param.get("employeeId")){
                request.setEmployeeId(param.get("employeeId").toString());
            }
            if(null != param.get("jobName")){
                request.setJobName(param.get("jobName").toString());
            }
            if(null != param.get("shopCodes")){
                request.setShopCodes((List<String>)param.get("shopCodes"));
            }
*/

            KylinGuideResponse<Object> response =  guideClient.updateGuideJobAndJurisdiction(JsonUtil.convert(param, GuideJobAndJurisdictionShopsParam.class));
            log.info("请求导购更新企业成员职位和管辖门店接口返回结果：{}",JsonUtil.serialize(response));
        }catch (Exception e){
            log.error("导购外部信息更新失败",e);
        }

    }
}
