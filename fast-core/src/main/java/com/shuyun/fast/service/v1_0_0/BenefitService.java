package com.shuyun.fast.service.v1_0_0;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shuyun.air.kylin.support.jackson.Jackson;
import com.shuyun.fast.auth.PassportClientAuth;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.cache.v1_0_0.BenefitCache;
import com.shuyun.fast.client.v1_0_0.BenefitMgmtClient;
import com.shuyun.fast.client.v1_0_0.BenefitServiceClient;
import com.shuyun.fast.client.v1_0_0.DataSelectorClient;
import com.shuyun.fast.domain.v1_0_0.SelectorYaql;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.event.domain.v1_0_0.BenefitProjectEvent;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.repository.BizCacheRepository;
import com.shuyun.fast.util.StreamQuery;
import com.shuyun.fast.v1_0_0.domain.SelectorData;
import com.shuyun.fast.v1_0_0.param.coupon.*;
import com.shuyun.fast.v1_0_0.result.CouponGetResult;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.ticket.base.domain.TicketRestrict;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import com.shuyun.ticket.benefit.domain.BenefitType;
import com.shuyun.ticket.benefit.domain.Denomination;
import com.shuyun.ticket.benefit.vo.Page;
import com.shuyun.ticket.benefit.vo.request.benefit.*;
import com.shuyun.ticket.benefit.vo.request.benefit.batchImport.BenefitBatchImportRequest;
import com.shuyun.ticket.benefit.vo.request.project.ProjectQueryVo;
import com.shuyun.ticket.benefit.vo.response.benefit.*;
import com.shuyun.ticket.benefit.vo.response.benefit.batchImport.BenefitBatchImportResponse;
import com.shuyun.ticket.util.JsonUtil;
import io.micronaut.cache.DefaultCacheManager;
import io.micronaut.cache.SyncCache;
import io.micronaut.cache.annotation.CacheInvalidate;
import io.micronaut.cache.annotation.Cacheable;
import io.micronaut.configuration.kafka.annotation.KafkaKey;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.Topic;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.util.StringUtils;
import io.micronaut.scheduling.annotation.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Singleton
@Slf4j
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class BenefitService {

    private final static ObjectMapper objectMapper = new ObjectMapper();
    static{
        Jackson.initialize(objectMapper);
    }

    private final BizCacheService bizCacheService;
    private final BenefitMgmtClient benefitMgmtClient;
    private final BenefitServiceClient benefitServiceClient;
    private final DataSelectorClient dataSelectorClient;
    private final SyncCache secondProjectCache;//延迟加载
    private final BizCacheRepository bizCacheRepository;

    private static final String tenantId = GlobalContext.defTenantId();
    private final Map<String, String> selectorIds = new ConcurrentHashMap<String, String>();
    private final Map<String, String> programSelectorOwners = new ConcurrentHashMap<String, String>();

    @Value("${micronaut.application.name}")
    private String serviceId;

    public BenefitService(PassportClientAuth passportClientAuth, //控制DI顺序,勿动
                          DefaultCacheManager cacheManager,
                          BenefitMgmtClient benefitMgmtClient,
                          BenefitServiceClient benefitServiceClient,
                          BizCacheService bizCacheService,
                          DataSelectorClient dataSelectorClient,
                          BizCacheRepository bizCacheRepository){
        this.benefitMgmtClient = benefitMgmtClient;
        this.benefitServiceClient = benefitServiceClient;
        this.bizCacheService = bizCacheService;
        this.dataSelectorClient = dataSelectorClient;
        this.bizCacheRepository = bizCacheRepository;
        this.secondProjectCache = cacheManager.getCache("secondProjectCache");
    }

    @PostConstruct
    public void init() {
        log.info("load benefit project cache start ...");
        List<BizCache> caches = (List<BizCache>)bizCacheRepository.findAll();
        caches = caches.stream()
                .filter(c->c.getTenantId().equals(tenantId))
                .filter(c->c.getCacheType().equals(BizCache.BENEFIT))
                .collect(Collectors.toList());
        caches.forEach(c->{
            BenefitCache bc = JsonUtil.listFromJson(c.getValue(), BenefitCache.class).get(0);
            if(StringUtils.isNotEmpty(bc.getSelectorOwner())){
                programSelectorOwners.put(bc.getProgramId(), bc.getSelectorOwner());
            }
            //初始化加载指定方案下全量券项目(延迟预热)
            load(bc.getProgramId());
        });
        log.info("load benefit project cache end ...");
        //选择器数据初始化加载
        loadAllSelectorData();
    }

    private void load(String programId){
        Integer page = 0;
        ProjectQueryVo query = new ProjectQueryVo();
        query.setProgram(programId);
        query.setPageSize(100);
        query.setQueryParams("");
        List<Map> projects;
        do{
            query.setPage(page++);
            Page<Map> pages = benefitMgmtClient.projectList(query);
            projects = pages.getData();
            projects.forEach(m->{
                BenefitProject project = com.shuyun.fast.util.JsonUtil.outPutConvert(m, BenefitProject.class);
                //初始化二级项目缓存
                secondProjectCache.put(String.join("_", programId, project.getId()), project);
                //选择器id缓存
                cacheSelectorId(programId, project);
            });
        }while(!projects.isEmpty());
    }


    /**
     * 每个实例每次启动都分配一个新的唯一的groupId,并且从latest开始消费
     * @param key
     * @param event
     * @param offset
     * @param partition
     * @param timestamp
     */
    @KafkaListener(value = "${benefit.consumer.group:benefit-consumer}", uniqueGroupId = true)
    @Topic(value = ModelTags.EVENT_TOPIC_BENEFIT_PROJECT)
    public void onProjectEvent(@KafkaKey String key,
                               BenefitProjectEvent event,
                               long offset,
                               int partition,
                               long timestamp){

        try {
            log.info("receive benefit project event:{}", JsonUtil.serialize(event));
            if(!serviceId.equals(event.getServiceId())){
                log.info("not my due to handle,ignore event");
                return ;
            }
            Thread.sleep(15000L);//后期优化写法
            //刷新项目缓存
            refreshProject(event.getProjectId());
        } catch (Exception e){
            log.error("handle project event error:", e);
        }
    }

    /**
     * 选择器数据动态刷新
     */
    @Scheduled(fixedRate = "${benefit.selector.refresh.rate}", initialDelay = "${benefit.selector.refresh.delay}")//固定2h执行1次,初始延迟20min
    public void loadAllSelectorData(){
        log.info("load select data at fixed time");
        Set<String> ids = selectorIds.keySet();
        ids.stream()
                .parallel()
                .forEach(id->{
                    refreshSelector(tenantId, id);
                });
    }

    /**
     * 暂不处理券项目对应的选择器频繁更新引发的废数据问题
     * @param programId
     * @param project
     */
    private void cacheSelectorId(String programId, BenefitProject project){
        String selectorOwner = programSelectorOwners.get(programId);
        TicketRestrict restrict;
        if("useRestrict".equals(selectorOwner)){
            restrict = project.getUseRestrict();
        }else{
            restrict = project.getRestrict();
        }
        if(Objects.nonNull(restrict) && StringUtils.isNotEmpty(restrict.getGoodsRef())){
            selectorIds.put(restrict.getGoodsRef(), project.getId());
        }
        if(Objects.nonNull(restrict) && StringUtils.isNotEmpty(restrict.getShopsRef())){
            selectorIds.put(restrict.getShopsRef(), project.getId());
        }
    }

    @Cacheable(cacheNames = "firstProjectCache", parameters = {"programId", "projectId"})
    public BenefitProject loadProject(String programId, String projectId){
        return benefitMgmtClient.projectDetail(projectId);
    }

    @CacheInvalidate(cacheNames = "firstProjectCache", parameters = {"programId", "projectId"})
    public void invalidProject(String programId, String projectId){
    }

    public void refreshProject(String projectId){
        CouponProjectGetParam param = new CouponProjectGetParam();
        param.setProjectId(projectId);
        param.setUseCache(false);
        //从卡券接口获取(非本地缓存)最新数据
        BenefitProject project = projectGet(param);
        String programId = project.getProgram();
        if(!programSelectorOwners.containsKey(programId)){
            log.error("programId {} do not exist in biz cache,please check your config", programId);
            return ;
        }
        //更新二级缓存
        secondProjectCache.put(String.join("_", programId, projectId), project);
        //失效一级缓存
        invalidProject(programId,projectId);
        //选择器id缓存刷新
        cacheSelectorId(programId, project);
        //选择器数据刷新
        String selectorOwner = programSelectorOwners.get(programId);
        TicketRestrict restrict;
        if("useRestrict".equals(selectorOwner)){
            restrict = project.getUseRestrict();
        }else{
            restrict = project.getRestrict();
        }
        if(StringUtils.isNotEmpty(restrict.getShopsRef())){
            refreshSelector(tenantId, restrict.getShopsRef());
        }
        if(StringUtils.isNotEmpty(restrict.getGoodsRef())){
            refreshSelector(tenantId, restrict.getGoodsRef());
        }
    }

    public BenefitProject getProjectCache(String programId, String projectId){
        String key = String.join("_", programId, projectId);
        Optional<BenefitProject> value = secondProjectCache.get(key, BenefitProject.class);
        return value.isEmpty() ? loadProject(programId, projectId):value.get();
    }

    public List<String> loadSelectorNoCache(String tenantId, @NotBlank String id){
        SelectorYaql selectorYaql = dataSelectorClient.select(id);
        List<String> result = new ArrayList<>();
        StreamQuery.streamQuery(selectorYaql.getYaql(), null, 1000, null, datas->{
            List<Object> dataList = new ArrayList<>();
            datas.stream().forEach(m->{
                dataList.addAll(m.values());
            });
            dataList.forEach(d->{
                result.add((String)d);
            });
            return null;
        });
        return result;
    }

    @Cacheable(cacheNames = "selectorCache", parameters = {"tenantId", "id"})
    public List<String> loadSelector(String tenantId, @NotBlank String id){
        SelectorYaql selectorYaql = dataSelectorClient.select(id);
        List<String> result = new ArrayList<>();
        StreamQuery.streamQuery(selectorYaql.getYaql(), null, 1000,10000L, datas->{
            List<Object> dataList = new ArrayList<>();
            datas.stream().forEach(m->{
                dataList.addAll(m.values());
            });
            dataList.forEach(d->{
                result.add((String)d);
            });
            return null;
        });
        return result;
    }

    @CacheInvalidate(cacheNames = "selectorCache", parameters = {"tenantId", "id"})
    public void invalidSelector(String tenantId, @NotBlank String id){
    }

    public void refreshSelector(String tenantId, @NotBlank String id){
        invalidSelector(tenantId, id);
        loadSelector(tenantId, id);
    }

    public BenefitCache bizCacheGet(ApiBaseParam param){
        BenefitCache cache = bizCacheService.get(BenefitCache.class, tenantId, param.getBizCode(), BizCache.BENEFIT)
                .stream()
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.BENEFIT);
        }
        return cache;
    }

    public <T extends BenefitBaseRequest> T setSceneParam(T request, CouponBaseParam param){
        BenefitCache cache = bizCacheGet(param);
        request.setTransactionId(param.getTransactionId());
        request.setAsync(false);
        request.setAtomic(param.getAtomic());
        request.setProgramId(cache.getProgramId());
        request.setSubjectFqn(cache.getSubjectFqn());
        request.setExtension(param.getExtension());
        return request;
    }

    public BenefitGrantResponse grant(CouponGrantParam param){
        if(Objects.nonNull(param.getIdentify())){
            BenefitGrantRequest request = this.setSceneParam(new BenefitGrantRequest(), param);
            request.setGrantNum(param.getGrantNum());
            request.setHolders(Arrays.asList(param.getIdentify().getMemberId()));
            request.setProjectId(param.getProjectId());
            request.setGrantPlatform(param.getGrantPlatform());
            request.setGrantShop(param.getGrantShop());
            request.setGrantReason(param.getGrantReason());
            return benefitServiceClient.grant(request);
        }else{
            BenefitCreateRequest request = this.setSceneParam(new BenefitCreateRequest(), param);
            request.setNum(param.getGrantNum());
            request.setProjectId(param.getProjectId());
            request.setGrantPlatform(param.getGrantPlatform());
            request.setGrantReason(param.getGrantReason());
            //匿名券发放
            return benefitServiceClient.create(request);
        }
    }

    public BenefitDiscardResponse grantRepeal(BenefitDiscardRequest param){
        return benefitServiceClient.discard(param);
    }

    public List<BenefitResponse> list(CouponListParam param){
        BenefitCache cache = bizCacheGet(param);
        return  benefitServiceClient.list("translate",
                cache.getProgramId(),
                cache.getSubjectFqn(),
                param.getProjectId(),
                param.getIdentify().getMemberId(),
                param.getState(),
                null,
                param.getQueryParams(),
                param.getPage(),
                param.getPageSize());
    }

    public BenefitAvailableListResponse availableList(BenefitAvailableListRequest param){
        return benefitServiceClient.availableList(param);
    }

    public BenefitUseResponse consume(BenefitUseRequest param){
        return benefitServiceClient.use(param);
    }

    public BenefitUseResponse consumeCheck(BenefitUseRequest param){
        return benefitServiceClient.checkUse(param);
    }

    public BenefitCancelUseResponse consumeRepeal(BenefitCancelUseRequest param){
        return benefitServiceClient.cancelUse(param);
    }

    public BenefitDiscountCalcResponse discountCalc(BenefitDiscountCalcRequest param) {
        return benefitServiceClient.discountCalc(param);
    }

    public BenefitResponse couponGet(CouponGetParam param){
        BenefitCache cache = bizCacheGet(param);
        return benefitServiceClient.instanceById("translate",
                cache.getProgramId(),
                null,
                param.getCode(),
                cache.getSubjectFqn(),
                param.getProjectId());
    }

    public BenefitLockResponse lock(BenefitLockRequest param) {
        return benefitServiceClient.lock(param);
    }

    public BenefitUnlockResponse unLock(BenefitUnlockRequest param) {
        return benefitServiceClient.unLock(param);
    }

    public BenefitTransferResponse transfer(BenefitTransferRequest param) {
        return benefitServiceClient.transfer(param);
    }

    public BenefitReceiveResponse receive(BenefitReceiveRequest param) {
        return benefitServiceClient.receive(param);
    }

    public BenefitReturnResponse returns(BenefitReturnRequest param) {
        return benefitServiceClient.returns(param);
    }

    public Page<Map> projectList(CouponProjectListParam param) {
        BenefitCache cache = bizCacheGet(param);
        ProjectQueryVo queryVo = new ProjectQueryVo();
        queryVo.setProgram(cache.getProgramId());
        queryVo.setPage(param.getPage());
        queryVo.setPageSize(param.getPageSize());
        queryVo.setQueryParams(param.getQueryParams());
        String sortParam = "{\"sortBy\":\"%s\",\"sortType\":\"%s\"}";
        queryVo.setOperatorParams(String.format(sortParam, param.getSortBy(), param.getSortType()));
        Page<Map> result = benefitMgmtClient.projectList(queryVo);
        return result;
    }
    public BenefitProject projectGet(CouponProjectGetParam param) {
        BenefitProject project;
        if(param.getUseCache()){
            BenefitCache cache = bizCacheGet(param);
            project = getProjectCache(cache.getProgramId(), param.getProjectId());
        }else{
            project = benefitMgmtClient.projectDetail(param.getProjectId());
        }
        return project;
    }

    public void fillProjectInfo(CouponGetResult cr, BenefitProject project){
        cr.setRuleText(project.getDescription());
        cr.setCouponName(project.getTitle());

        //兑换券
        if (BenefitType.EXCHANGE.getCode().equals(project.getType().getCode())) {
            if (StringUtils.isNotEmpty(project.getPrivilegeType().getCode())){
                cr.setCouponType(project.getPrivilegeType().getCode());
            }
        } else {
            //优惠券 和内购券 ,优惠券又分为折扣和抵扣
            if(BenefitType.DISCOUNT.getCode().equals(project.getType().getCode())
                    || "INTERNAL_PURCHASE".equals(project.getType().getCode())){
                cr.setCouponType(project.getDiscount().getMode().getCode());
            }
        }
        if(Objects.nonNull(project.getDiscount())){
            Denomination amount = project.getDiscount().getAmount();
            if(Objects.nonNull(amount)){
                cr.setReductAmount(amount.getValue());
                cr.setDiscountRate(amount.getDiscountValue());
                cr.setThresholdValue(Objects.nonNull(amount.getThreshold())?amount.getThreshold().getValue():null);
            }
        }
    }

    //填充选择器数据
    public SelectorData fillSelectorDataNoCache(ApiBaseParam param, SelectorData sd, BenefitProject project){
        BenefitCache cache = bizCacheGet(param);
        TicketRestrict restrict;
        if("useRestrict".equals(cache.getSelectorOwner())){
            restrict = project.getUseRestrict();
        }else{
            restrict = project.getRestrict();
        }
        if(Objects.nonNull(restrict) && StringUtils.isNotEmpty(restrict.getShopsRef())){
            String selectorId = restrict.getShopsRef();
            switch(restrict.getShopsRefType()){
                case INCLUDE:
                    sd.setIncludeShops(loadSelectorNoCache(tenantId, selectorId));
                    break;
                case EXCLUDE:
                    sd.setExcludeShops(loadSelectorNoCache(tenantId, selectorId));
                    break;
                case UNRESTRICT:
                    log.debug("this shop selector has no restrict");
                    break;
                default:
                    throw new IllegalArgumentException("error shop selector type");
            }
        }
        if(Objects.nonNull(restrict) && StringUtils.isNotEmpty(restrict.getGoodsRef())){
            String selectorId = restrict.getGoodsRef();
            switch(restrict.getGoodsRefType()){
                case INCLUDE:
                    sd.setIncludeGoods(loadSelectorNoCache(tenantId, selectorId));
                    break;
                case EXCLUDE:
                    sd.setExcludeGoods(loadSelectorNoCache(tenantId, selectorId));
                    break;
                case ALL_INCLUDE:
                    sd.setAnyIncludeGoods(loadSelectorNoCache(tenantId, selectorId));
                    break;
                case ANY_EXCLUDE:
                    sd.setAnyExcludeGoods(loadSelectorNoCache(tenantId, selectorId));
                    break;
                case UNRESTRICT:
                    log.debug("this good selector has no restrict");
                    break;
                default:
                    throw new IllegalArgumentException("error good selector type");
            }
        }
        if(Objects.nonNull(project.getExchange()) && StringUtils.isNotEmpty(project.getExchange().getGoods())){
            String selectorId = project.getExchange().getGoods();
            try {
                sd.setExchangeGoods(loadSelectorNoCache(tenantId, selectorId));
            } catch (Exception e) {
                throw new IllegalArgumentException("error exchangeGoods selector id");
            }
        }
        return sd;
    }

    //填充选择器数据
    public SelectorData fillSelectorData(ApiBaseParam param, SelectorData sd, BenefitProject project){
        BenefitCache cache = bizCacheGet(param);
        TicketRestrict restrict;
        if("useRestrict".equals(cache.getSelectorOwner())){
            restrict = project.getUseRestrict();
        }else{
            restrict = project.getRestrict();
        }
        if(Objects.nonNull(restrict) && StringUtils.isNotEmpty(restrict.getShopsRef())){
            String selectorId = restrict.getShopsRef();
            switch(restrict.getShopsRefType()){
                case INCLUDE:
                    sd.setIncludeShops(loadSelector(tenantId, selectorId));
                    break;
                case EXCLUDE:
                    sd.setExcludeShops(loadSelector(tenantId, selectorId));
                    break;
                case UNRESTRICT:
                    log.debug("this shop selector has no restrict");
                    break;
                default:
                    throw new IllegalArgumentException("error shop selector type");
            }
        }
        if(Objects.nonNull(restrict) && StringUtils.isNotEmpty(restrict.getGoodsRef())){
            String selectorId = restrict.getGoodsRef();
            switch(restrict.getGoodsRefType()){
                case INCLUDE:
                    sd.setIncludeGoods(loadSelector(tenantId, selectorId));
                    break;
                case EXCLUDE:
                    sd.setExcludeGoods(loadSelector(tenantId, selectorId));
                    break;
                case ALL_INCLUDE:
                    sd.setAnyIncludeGoods(loadSelector(tenantId, selectorId));
                    break;
                case ANY_EXCLUDE:
                    sd.setAnyExcludeGoods(loadSelector(tenantId, selectorId));
                    break;
                case UNRESTRICT:
                    log.debug("this good selector has no restrict");
                    break;
                default:
                    throw new IllegalArgumentException("error good selector type");
            }
        }
        if(Objects.nonNull(project.getExchange()) && StringUtils.isNotEmpty(project.getExchange().getGoods())){
            String selectorId = project.getExchange().getGoods();
            try {
                sd.setExchangeGoods(loadSelector(tenantId, selectorId));
            } catch (Exception e) {
                throw new IllegalArgumentException("error exchangeGoods selector id");
            }
        }
        return sd;
    }

    /**
     * 产品入参的时间格式有要求,用序列化适配
     * @param request
     * @return
     * @throws Exception
     */
    public BenefitBatchImportResponse importBatch(@Valid BenefitBatchImportRequest request) throws Exception{
        return benefitServiceClient.importBatch(objectMapper.writeValueAsString(request));
    }
}
