package com.shuyun.fast.service.v1_0_0;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.config.BojunConfiguration;
import com.shuyun.fast.kafka.KafkaSender;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @title BojunInterfaceService
 * @description 伯俊接口调用
 * @create 2024/9/5 15:50
 */
@Singleton
@Slf4j
public class BojunInterfaceService {

    private KafkaSender kafkaSender;
    private BojunConfiguration bojunConfiguration;

    @Inject
    private RedissonClient redissonClient;

    public BojunInterfaceService(KafkaSender kafkaSender,
                                 BojunConfiguration bojunConfiguration){
        this.kafkaSender = kafkaSender;
        this.bojunConfiguration = bojunConfiguration;
    }

    final private static String GET_TOKEN_INTERFACE = "/api/auth/login";

    final private static String POINT_SYNC_INTERFACE = "/api/ip/qianbaidu/v1/shuyun/noticeInte";

    final private static String BOJUN_TOKEN_KEY = "BOJUN_TOKEN_KEY";



    public void doPost(JSONObject param) throws Exception{
        String token = getToken();
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,param.toJSONString());
        Request request = new Request.Builder()
                .url(bojunConfiguration.getUrl()+POINT_SYNC_INTERFACE)
                .method("POST", body)
                .addHeader("r3-api-token", token)
                .addHeader("Midend-correlation-id", System.currentTimeMillis()+"")
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", "sessionCookie=43031b18-640b-4476-82f7-8310129f1320")
                .build();
        long startTime = System.currentTimeMillis();
        Response response = client.newCall(request).execute();
        long endTime = System.currentTimeMillis();
        log.info("积分明细同步伯俊接口响应：{}，耗时：{}ms",response,endTime-startTime);
    }


    private String getToken() throws Exception{
        RBucket<String> bucket = redissonClient.getBucket(BOJUN_TOKEN_KEY);
        String token = bucket.get();
        if(StringUtils.isNotBlank(token)){
            return token;
        }
       String userName = bojunConfiguration.getUserName();
       String userKey = bojunConfiguration.getUserKey();
       String requestSign = bojunConfiguration.getRequestSign();
       String url = bojunConfiguration.getUrl();
       StringBuffer requestUrl = new StringBuffer();
       requestUrl.append(url).append(GET_TOKEN_INTERFACE).append("?").append("userName=").append(userName).append("&userKey=").append(userKey).append("&requestSign=").append(requestSign);
       log.info("request bojun url:{}",requestUrl.toString());
       OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        Request request = new Request.Builder()
                .url(requestUrl.toString())
                .method("GET", null)
                .addHeader("Cookie", "sessionCookie=43031b18-640b-4476-82f7-8310129f1320")
                .build();
        long startTime = System.currentTimeMillis();
        Response response = client.newCall(request).execute();
        long endTime = System.currentTimeMillis();
        log.info("调用伯俊鉴权接口：{}，耗时：{}ms",response.body(),endTime-startTime);
        if(response.isSuccessful() && response.body() != null){
            //if(JSONObject.parseObject(response.body().string()).getBoolean("success")){
                token = JSONObject.parseObject(response.body().string()).getString("loginToken");
                bucket.set(token, 3600, TimeUnit.SECONDS);
            //}
        }
        return token;
    }

    private  String MD5(String input) {
        try {
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            mdInst.update(input.getBytes(Charset.forName("UTF-8")));
            byte[] md = mdInst.digest();
            StringBuffer hexString = new StringBuffer();
            for (int i = 0; i < md.length; i++) {
                String shaHex = Integer.toHexString(md[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("error:", e);
        }
        return "";
    }


}
