package com.shuyun.fast.service.v1_0_0;


import com.alibaba.fastjson.JSONObject;

import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.kafka.KafkaSender;
import com.shuyun.fast.util.StrUtil;
import com.shuyun.fast.v1_0_0.param.nancent.DataJson;
import com.shuyun.fast.v1_0_0.param.nancent.Member;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @title NancentService
 * @description 南讯数据接入
 * @create 2024/11/25 16:27
 */
@Singleton
@Slf4j
public class NancentService {
    //final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
    //private  JdbcTemplate jdbcTemplate;
    private final KafkaSender kafkaSender;
    public NancentService(KafkaSender kafkaSender){
        this.kafkaSender = kafkaSender;
    }

    public void nancentTaobaoMemberSave(JSONObject param) throws Exception{
        String id = StrUtil.MD5(param.getString("ouid")+param.getString("sellerNick")).toUpperCase();
        /*String querySql = "select id from nancent_his_taobao_member where id='"+id+"'";
        List<Map<String,Object>> dataList = jdbcTemplate.queryForList(querySql);*/
        //JSONObject param = JSONObject.parseObject(JSONObject.toJSON(member).toString());
        param.put("id",id);
        kafkaSender.send(ModelTags.EVENT_TOPIC_NANCENR_TAOBAO_MEMBER_SAVE, UUID.randomUUID().toString(),param);
    }

    public void nancentTaobaoOrderSave(JSONObject param) throws Exception{
        String id = StrUtil.MD5(param.getString("tid")+param.getString("sellerNick")).toUpperCase();
        /*String querySql = "select id from nancent_his_taobao_member where id='"+id+"'";
        List<Map<String,Object>> dataList = jdbcTemplate.queryForList(querySql);*/
        //JSONObject param = JSONObject.parseObject(JSONObject.toJSON(member).toString());
        param.put("id",id);
        kafkaSender.send(ModelTags.EVENT_TOPIC_NANCENR_TAOBAO_ORDER_SAVE, UUID.randomUUID().toString(),param);
    }
    public void nancentTaobaoRefundSave(JSONObject param) throws Exception{
        String id = StrUtil.MD5(param.getString("tid")+param.getString("sellerNick")).toUpperCase();
        /*String querySql = "select id from nancent_his_taobao_member where id='"+id+"'";
        List<Map<String,Object>> dataList = jdbcTemplate.queryForList(querySql);*/
        //JSONObject param = JSONObject.parseObject(JSONObject.toJSON(member).toString());
        param.put("id",id);
        kafkaSender.send(ModelTags.EVENT_TOPIC_NANCENR_TAOBAO_REFUND_SAVE, UUID.randomUUID().toString(),param);
    }

}
