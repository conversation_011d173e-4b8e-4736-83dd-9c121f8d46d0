package com.shuyun.fast.service.v1_0_0;


import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.v1_0_0.domain.DeliveryChannelSPI;
import com.shuyun.fast.v1_0_0.domain.DeliveryChannelStatus;
import com.shuyun.fast.v1_0_0.domain.GiftSpecificationsSPI;
import com.shuyun.fast.v1_0_0.domain.ShelfStatus;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @title BenefitSpiService
 * @description
 * @create 2025/8/13 14:18
 */

@Slf4j
@Singleton
public class BenefitSpiService {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    public List<DeliveryChannelSPI> getChannelSpi(){
        //BaseResponse<Map> execute = dataapiHttpSdk.execute("select channelCode,channelType from data.offer.gjChannel", Collections.emptyMap());
        //log.info("查询渠道信息：{}",execute);
        List<DeliveryChannelSPI> channelSPIs=new ArrayList<>();
        //List<Map> data = execute.getData();
       // if(null!=data && !data.isEmpty()){
            //log.info("渠道信息：{}",data);
            DeliveryChannelSPI channelSPI = new DeliveryChannelSPI();
            channelSPI.setChannelID("WECHAT");
            channelSPI.setChannelName("微信");
            channelSPI.setChannelStatus(DeliveryChannelStatus.ENABLED);
            channelSPIs.add(channelSPI);
        //}
        log.info("渠道信息查询结束：{}",channelSPIs);
        return channelSPIs;
    }

    public List<GiftSpecificationsSPI> getGiftSpecifications() {
        List<GiftSpecificationsSPI> giftSpecificationsSPIs = new ArrayList<>();
        int totalCount = getTotalCount();
        int pageSize = 500;
        int totalPage = (totalCount / 500) + 1;
        for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
            String sql = "select memberType,productCode,productName,productDesc,eanCode,sqId,tagPrice,retailPrice,deptCode,deptName,familyCode,familyName,subFamilyCode,subFamilyName,material,season,brand,size,color,onSaleYear,onSaledate,picture,isValid,channelType,createTime,updateTime,lastSync from "+String.format(ModelTags.DATA_FQN_MDM_PRODUCT, "CBANNER")+"  where brand='赠品' limit "+ pageSize + " OFFSET " + (currentPage - 1) * pageSize;
            List<Map<String,Object>> data = dataapiHttpSdk.execute(sql,new HashMap<>()).getData();
            log.info("查询商品信息：{}",data);
            for (Map<String,Object> map : data){
                GiftSpecificationsSPI giftSpecificationsSPI = new GiftSpecificationsSPI();
                giftSpecificationsSPI.setProductCode((String) map.get("productCode"));
                giftSpecificationsSPI.setProductName((String) map.get("productName"));
                giftSpecificationsSPI.setSkuID((String) map.get("productCode"));
                giftSpecificationsSPI.setSkuName((String) map.get("productName"));
                giftSpecificationsSPI.setProductDesc((String) map.get("productDesc"));
                giftSpecificationsSPI.setTagPrice((BigDecimal) map.get("tagPrice"));
                giftSpecificationsSPI.setRetailPrice(map.get("retailPrice")==null?null:((BigDecimal)map.get("retailPrice")).doubleValue() );
                giftSpecificationsSPI.setDeptCode((String) map.get("deptCode"));
                giftSpecificationsSPI.setDeptName((String) map.get("deptName"));
                giftSpecificationsSPI.setFamilyCode((String) map.get("familyCode"));
                giftSpecificationsSPI.setFamilyName((String) map.get("familyName"));
                giftSpecificationsSPI.setSubFamilyCode((String) map.get("subFamilyCode"));
                giftSpecificationsSPI.setSubFamilyName((String) map.get("subFamilyName"));
                giftSpecificationsSPI.setMaterial((String) map.get("material"));
                giftSpecificationsSPI.setSeason((String) map.get("season"));
                giftSpecificationsSPI.setBrand((String) map.get("brand"));
                giftSpecificationsSPI.setSize((String) map.get("size"));
                giftSpecificationsSPI.setColor((String) map.get("color"));
                giftSpecificationsSPI.setTotalInventory(99999);//Fiskars 售后沟通群 王宇要求写99999
                giftSpecificationsSPI.setAvailableInventory(99999);//Fiskars 售后沟通群 王宇要求写99999
                String isValid = (String) map.get("isValid");
                if ("Y".equals(isValid)){
                    giftSpecificationsSPI.setStatus(ShelfStatus.ON_SALE);
                }else if ("N".equals(isValid)){
                    giftSpecificationsSPI.setStatus(ShelfStatus.OFF_SALE);
                }
                giftSpecificationsSPI.setCreateTime((String) map.get("createTime"));
                giftSpecificationsSPI.setUpdateTime((String) map.get("updateTime"));
                giftSpecificationsSPI.setLastSyncDateTime((String) map.get("lastSync"));
                giftSpecificationsSPI.setOnSaleYear((String) map.get("onSaleYear"));
                giftSpecificationsSPI.setOnSaleDate((String) map.get("onSaleDate"));
                Object pictureObj = map.get("picture");
                if(null!= pictureObj) {
                    if (pictureObj instanceof List<?>) {
                        List<?> pictureList = (List<?>) pictureObj;
                        List<String> stringPictureList = new ArrayList<>();
                        for (Object item : pictureList) {
                            if (item instanceof String) {
                                stringPictureList.add((String) item);
                            }
                        }
                        giftSpecificationsSPI.setPicture(stringPictureList);
                    } else {
                        giftSpecificationsSPI.setPicture(Collections.emptyList()); // 默认空列表避免 null
                    }
                }
                giftSpecificationsSPI.setEanCode((String) map.get("eanCode"));
                giftSpecificationsSPI.setSqId((String) map.get("sqId"));
                giftSpecificationsSPI.setChannelID((String) map.get("channelType"));
                giftSpecificationsSPIs.add(giftSpecificationsSPI);
            }
        }
        return giftSpecificationsSPIs;
    }

    public int getTotalCount(){
        String sql = "select count(1) as counts from "+String.format(ModelTags.DATA_FQN_MDM_PRODUCT, "CBANNER")+" where brand='赠品'";
        BaseResponse execute = dataapiHttpSdk.execute(sql,new HashMap<>());
        List<Map<String,Object>> data = execute.getData();
        if(data!=null && data.size()>0){
            return new Integer(String.valueOf(data.get(0).get("counts")));
        }
        return 0;
    }

}
