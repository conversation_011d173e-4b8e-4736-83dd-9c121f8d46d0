package com.shuyun.fast.service.v1_0_0;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.dataapi.criteria.CriteriaBuilder;
import com.shuyun.dm.api.dataapi.request.QueryDataRequest;
import com.shuyun.dm.api.vo.PageQueryResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.epassport.sdk.Org;
import com.shuyun.epassport.sdk.Page;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.client.v1_0_0.EpassportClient;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.util.ModelUtil;
import com.shuyun.fast.v1_0_0.domain.Department;
import com.shuyun.fast.v1_0_0.domain.OrgResult;
import com.shuyun.fast.v1_0_0.param.department.DepartmentInitOrgParam;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@Singleton
public class DepartmentService {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
    @Inject
    private EpassportClient epassportClient;

    public ApiResult<Void> initOrg(DepartmentInitOrgParam param) {
        //查询所有部门数据
        List<Department> allDepartmentList = this.queryAllDepartment();
        log.info("init org all department list size = {}", allDepartmentList.size());
        //部门数据按照level排序，并创建组织
        allDepartmentList.sort(Comparator.comparing(Department::getLevel));
        //部门编码映射部门，方便子部门取parentId
        Map<String, OrgResult> orgMap = new HashMap<>();
        for (Department department : allDepartmentList) {
            log.info("init org department = {}", JsonUtil.outPutSerialize(department));
            //先查询是否已存在组织数据
            OrgResult org = getOrg(department);
            if (Objects.isNull(org)) {
                Long parentId;
                if (department.getLevel() == 1) {
                    //当level=1，层级在全公司下（全公司id=1）
                    parentId = 1L;
                } else {
                    OrgResult parentOrg = orgMap.get(department.getParentId());
                    if (Objects.isNull(parentOrg)) {
                        log.error("init org failed, not find parent org in orgMap, departmentId={}", department.getId());
                        return ApiResult.failure(String.valueOf(ApiTags.API_RESP_CODE_500100), "组织创建失败，未找到上级部门");
                    }
                    parentId = parentOrg.getId();
                }
                org = this.createOrg(department, parentId);
                if (Objects.isNull(org) || Objects.isNull(org.getId())) {
                    return ApiResult.failure(String.valueOf(ApiTags.API_RESP_CODE_500100), "组织创建失败");
                }
            }
            orgMap.put(department.getCode(), org);
        }
        return ApiResult.success();
    }

    public void syncOrg(JSONObject event) {
        Department department = JsonUtil.outPutDeserialize(event.toJSONString(), Department.class);
        //查询组织数据
        OrgResult org = getOrg(department);
        if (BooleanUtil.isFalse(department.getEnable())) {
            //部门已删除
            if (Objects.isNull(org)) {
                //如果组织已删除，则不做处理
                log.info("sync org ignore, not find org and department is not enable, departmentId={}", department.getId());
            } else {
                //先只删除本级组织(假设kafka监听首先收到的是子级组织，否则有子级未删除直接删除本级报错)
                log.info("sync org department is not enable, delete org, departmentId={}", department.getId());
                epassportClient.delete(org.getId());
            }
            return;
        }
        //查询当前部门父级组织id
        Long departmentParentOrgId = getDepartmentParentId(department);
        if (Objects.isNull(departmentParentOrgId)) {
            return;
        }
        //查到组织更新，没查到则新建
        if (Objects.isNull(org)) {
            OrgResult newOrg = this.createOrg(department, departmentParentOrgId);
            if (Objects.isNull(newOrg) || Objects.isNull(newOrg.getId())) {
                log.error("sync org create org failed, departmentId={}, departmentParentOrgId={}", department.getId(), departmentParentOrgId);
                return;
            }
        } else {
            //更新
            this.updateOrg(department, departmentParentOrgId, org);
        }
    }

    private List<Department> queryAllDepartment() {
        List<Department> allDepartmentList = new ArrayList<>();
        int currentPage = 1;
        int pageSize = 1000;

        QueryDataRequest query = new QueryDataRequest();
        query.setFields(ModelUtil.getFieldNames("", Department.class));
        query.setFqn(ModelTags.DATA_FQN_DEPARTMENT);
        query.setWithTotals(true);
        query.setLimit(pageSize);
        while (true) {
            query.setOffset((currentPage - 1) * pageSize);
            PageQueryResponse pageResponse = dataapiHttpSdk.queryObjects(query);
            List<Map<String, Object>> data = pageResponse.getData();
            //没有数据跳出循环
            if (CollUtil.isEmpty(data)) {
                break;
            }
            List<Department> currentDeparmentList = JsonUtil.outPutListFromJson(JsonUtil.outPutSerialize(pageResponse.getData()), Department.class);
            allDepartmentList.addAll(currentDeparmentList);
            //最后一页
            if (pageResponse.getTotals() <= currentPage * pageSize) {
                break;
            }
            currentPage++;
        }
        return allDepartmentList;
    }

    private Department queryDepartment(String code) {
        QueryDataRequest query = new QueryDataRequest();
        query.setFields(ModelUtil.getFieldNames("", Department.class));
        CriteriaBuilder cb = CriteriaBuilder.newBuilder();
        cb.and(CriteriaBuilder.eq("code", code));
        query.setFilter(cb.build());
        query.setFqn(ModelTags.DATA_FQN_DEPARTMENT);
        query.setWithTotals(true);
        query.setOffset(0);
        query.setLimit(1);
        PageQueryResponse pageResponse = dataapiHttpSdk.queryObjects(query);
        List<Map<String, Object>> data = pageResponse.getData();
        if (CollUtil.isNotEmpty(data)) {
            return JsonUtil.outPutConvert(pageResponse.getData().get(0), Department.class);
        }
        return null;
    }

    private OrgResult getOrg(Department department) {
        //查询组织数据，优先根据shopCode查，查不到再根据code查
        String code = department.getCode();
        if (StrUtil.isNotBlank(department.getShopCode())) {
            code = department.getShopCode();
        }
        Page<OrgResult> list = epassportClient.list(code, null);
        if (list.getTotal() > 0) {
            return list.getData().get(0);
        }
        if (StrUtil.isNotBlank(department.getShopCode())) {
            //根据shopCode没查到
            list = epassportClient.list(department.getCode(), null);
            if (list.getTotal() > 0) {
                return list.getData().get(0);
            }
        }
        return null;
    }

    private Long getDepartmentParentId(Department department) {
        if (department.getLevel() == 1) {
            //当level=1，层级在全公司下（全公司id=1）
            return 1L;
        } else {
            //查询父级部门数据
            String parentId = department.getParentId();
            Department parentDepartment = queryDepartment(parentId);
            if (Objects.isNull(parentDepartment)) {
                log.error("not find parent department, parentId={}", parentId);
                return null;
            }
            //查询父级组织
            OrgResult parentOrg = getOrg(parentDepartment);
            if (Objects.isNull(parentOrg)) {
                log.error("not find parent org, parentDepartment={}", JsonUtil.outPutSerialize(parentDepartment));
                return null;
            }
            return parentOrg.getId();
        }
    }

    private OrgResult createOrg(Department department, Long parentId) {
        log.info("create org, departmentId={}, parentId={}", department.getId(), parentId);
        Org org = BeanUtil.copyProperties(department, Org.class, "id", "parentId");
        //部门表与组织表 type差1
        if (Objects.nonNull(department.getType())) {
            org.setType(department.getType() + 1);
        }
        //有店铺编码，优先使用店铺编码
        if (StrUtil.isNotBlank(department.getShopCode())) {
            org.setCode(department.getShopCode());
        }
        org.setParentId(parentId);
        if (Objects.nonNull(org.getType()) && org.getType() == 7) {
            //如果组织是门店，需设置经营范围，经营范围-默认
            org.setBusinessScopeId("1");
        }
        try {
            return epassportClient.create(org);
        } catch (Exception e) {
            log.error("创建组织异常, org={}", JsonUtil.outPutSerialize(org), e);
        }
        return null;
    }

    private void updateOrg(Department department, Long departmentParentOrgId, OrgResult originalOrg) {
        log.info("update org, departmentId={}, departmentParentOrgId={}, originalOrg={}", department.getId(), departmentParentOrgId, JsonUtil.outPutSerialize(originalOrg));
        //只更新组织code、name、type，parentId
        String departmentCode = department.getCode();
        if (StrUtil.isNotBlank(department.getShopCode())) {
            departmentCode = department.getShopCode();
        }
        if (departmentCode.equals(originalOrg.getCode()) && department.getName().equals(originalOrg.getName())
                && Objects.equals(department.getType() + 1, originalOrg.getType()) && Objects.equals(departmentParentOrgId, originalOrg.getParentId())) {
            log.info("update org ignore, code、name、type、parentId no change, departmentId={}", department.getId());
            return;
        }
        Org updateOrg = BeanUtil.copyProperties(originalOrg, Org.class, "created", "updated");
        updateOrg.setCode(departmentCode);
        updateOrg.setName(department.getName());
        updateOrg.setType(department.getType() + 1);
        updateOrg.setParentId(departmentParentOrgId);
        if (updateOrg.getType() == 7) {
            //如果组织是门店，需设置经营范围，经营范围-默认
            updateOrg.setBusinessScopeId(ObjectUtil.defaultIfBlank(updateOrg.getBusinessScopeId(), "1"));
        } else {
            updateOrg.setBusinessScopeId(null);
        }
        log.info("update org, departmentId={}, updateOrg={}", department.getId(), JsonUtil.outPutSerialize(updateOrg));
        try {
            epassportClient.update(updateOrg.getId(), updateOrg);
        } catch (Exception e) {
            log.error("更新组织异常, updateOrg={}", JsonUtil.outPutSerialize(updateOrg), e);
        }
    }
}
