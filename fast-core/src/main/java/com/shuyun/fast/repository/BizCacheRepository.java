package com.shuyun.fast.repository;

import com.shuyun.fast.entity.BizCache;
import io.micronaut.data.jdbc.annotation.JdbcRepository;
import io.micronaut.data.model.query.builder.sql.Dialect;
import io.micronaut.data.repository.CrudRepository;

import java.util.Optional;

@JdbcRepository(dialect = Dialect.MYSQL)
public interface BizCacheRepository extends CrudRepository<BizCache, Long>{

    Optional<BizCache> find(String tenantId, String bizCode, String cacheType);
}
