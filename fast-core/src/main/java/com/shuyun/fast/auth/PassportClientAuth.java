package com.shuyun.fast.auth;

import com.shuyun.lite.client.Passport;
import com.shuyun.lite.client.PassportClientFactory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.util.StringUtils;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Singleton
@Requires(property = "passport.enable", value = "true", defaultValue = "false")
public class PassportClientAuth {

    @Value("${passport.clientId}")
    private String clientId;
    @Value("${passport.clientName}")
    private String clientName;

    @PostConstruct
    public void authInit() {
        if(StringUtils.isNotEmpty(clientId)){
            Passport passport = PassportClientFactory.instance();
            Map<String, Object> body = new HashMap<>();
            body.put("clientId", clientId);
            body.put("clientName", clientName);
            body.put("version", 2);
            String secret = passport.registerClient(body);
            log.info("服务:{}注册后返回secret:{}", clientId, secret);
        }else{
            log.info("passport.clientId参数未配置,不注册");
        }

    }
}
