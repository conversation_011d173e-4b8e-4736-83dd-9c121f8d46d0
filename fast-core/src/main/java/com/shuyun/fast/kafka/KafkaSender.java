package com.shuyun.fast.kafka;

import io.micronaut.configuration.kafka.annotation.KafkaClient;
import io.micronaut.configuration.kafka.annotation.KafkaKey;
import io.micronaut.configuration.kafka.annotation.Topic;


@KafkaClient
public interface KafkaSender {
    void send(@Topic String topic, @KafkaKey String key, String body);
    <T> void send(@Topic String topic, @KafkaKey String key, T body);
}

