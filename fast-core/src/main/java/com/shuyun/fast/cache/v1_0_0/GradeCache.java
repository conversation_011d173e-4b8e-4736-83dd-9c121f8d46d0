package com.shuyun.fast.cache.v1_0_0;

import com.shuyun.fast.annotation.BizCacheType;
import io.micronaut.core.annotation.Introspected;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@BizCacheType("grade")
public class GradeCache extends BaseCache{
    private Long planId;
    private Long gradeHierarchyid;
    private Long gradeId;
    private String gradeName;
    private String gradeBizType;
}
