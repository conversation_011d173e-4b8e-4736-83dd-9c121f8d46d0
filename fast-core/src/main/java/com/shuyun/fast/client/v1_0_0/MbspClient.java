package com.shuyun.fast.client.v1_0_0;
import com.shuyun.kylin.member.api.request.*;
import com.shuyun.kylin.member.api.response.*;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Post;
import io.micronaut.http.annotation.Produces;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.Valid;

@Client(id = "mbsp-api", path = "/mbsp-api/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface MbspClient {
    @Operation(summary = "注册会员", description = "注意：不支持传外部会员id")
    @Post("/member/register")
    @Consumes(MediaType.APPLICATION_JSON)
    RegisterResult register(@Valid @Body RegisterRequest request);

    @Operation(summary = "绑定会员", description = "注意：绑定时，会员id需要在系统中存在")
    @Post("/member/bind")
    @Consumes(MediaType.APPLICATION_JSON)
    BindResult bind(@Valid @Body BindRequest request);

    @Operation(summary = "创建会员", description = "注意：直接创建会员,支持传入memberId参数(历史数据导入场景)，若会员id已存在将会报错")
    @Post("/member/create")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberCreateResult createMember(@Valid @Body MemberCreateRequest request);

    @Operation(summary = "解绑渠道会员", description = "注意：1. 若当前会员所有渠道关系都解绑后，将会清空此会员跨渠道匹配标识<br/>"
            + "2. 若渠道会员不存在，将会报错 ；只能解绑生效会员状态下的渠道会员")
    @Post("/member/channel/unbind")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult unbindChannelMember(@Valid @Body ChannelMemberUnbindRequest request);

    @Operation(summary = "修改渠道会员信息", description = "说明只能修改生效状态下的渠道会员,若渠道会员不存在将会报错 ")
    @Post("/member/channel/modify")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberUpdateResult updateChannelMember(@Valid @Body ChannelMemberEditRequest request);

    @Operation(summary = "修改会员信息", description = "说明只能修改生效状态或者冻结下的会员信息")
    @Post("/member/modify")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult updateMember(@Valid @Body MemberEditRequest request);

    @Operation(summary = "手动合并会员", description = "说明:1. 会员手动合卡。外部系统需要确保参与合并的会员id在系统里面都存在且为有效会员。" +
            "<br/>2. mobile参数:会员合并时直接更新为主会员的手机号。")
    @Post("/member/merge")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult memberMerge(@Valid @Body MemberMergeRequest request);

    @Operation(summary = "变更会员匹配标识的值", description = "说明:本接口适用于修改会员的证件、匹配email等全渠道匹配标识的值。")
    @Post("/member/modifyIdentify")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult updateMemberIdentify(@Valid @Body MemberIdentifyModifyRequest request);

    @Operation(summary = "修改会员手机号", description = "说明:本接口适用于线下渠道修改会员的匹配手机号")
    @Post("/member/modifyMobile")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult modifyMobile(@Valid @Body MemberMobileModifyRequest request);

    @Operation(summary = "注销会员")
    @Post("/member/cancel")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult memberCancellation(@Valid @Body MemberCancellationRequest request);

    @Operation(summary = "失效会员", description = "该功能仅限会籍模型使用")
    @Post("/member/inactive")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult inactiveMember(@Valid @Body MemberStatusModifyRequest request);

    @Operation(summary = "冻结会员")
    @Post("/member/frozen")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult frozenMember(@Valid @Body MemberStatusModifyRequest request);

    @Operation(summary = "解冻会员")
    @Post("/member/unfrozen")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult unfrozenMember(@Valid @Body MemberStatusModifyRequest request);

    @Operation(summary = "根据会员业务字段查询会员信息", description = "说明1. memberId和 customizedQuery用于检索会员。两者选其一必传,当均有值的前提下 memberId优先 ;<br/>"
            + " 2.此接口默认查询 MemberQueryData类定义的标准会员字段<br/>"
            + " 3.customizedQuery参数:会员匹配自定义查询参数key-value，key:会员模型中的字段(查询字段索引请自行维护)<br/>"
            + " 4.optionalFields:若发现默认查询返回字段不满足需求，可以用此参数传入想要额外查询的会员字段列表(会员身上的所有字段),自定义字段查询结果将在optionalFieldData透出<br/>"
            + " 5.默认查询生效以及冻结状态的会员，若会员失效则不返回<br/>"
            + " 6.当会员不存在时，data返回null"
    )
    @Post("/member/query")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberQueryResult query(@Valid @Body MemberQueryRequest request);

    @Operation(summary = "根据跨渠道会员匹配标识查询会员信息", description = "说明1. 本接口适用于根据mobile等跨渠道会员匹配标识查询会员信息<br/>"
            + " 2.此接口默认查询 MemberQueryData类定义的标准会员字段<br/>"
            + " 3.optionalFields:若发现默认查询返回字段不满足需求，可以用此参数传入想要额外查询的会员字段列表(会员身上的所有字段),自定义字段查询结果将在optionalFieldData透出<br/>"
            + " 4.默认查询生效以及冻结状态的会员，若会员失效则不返回<br/>"
            + " 5.当会员不存在时，data返回null"
    )
    @Post("/member/identify")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberQueryResult queryByIdentify(@Valid @Body MemberQueryByIdentifyRequest request);

    @Operation(summary = "根据会员身份匹配策略查询会员信息", description = "说明:"
            + " 1. 此接口将会根据系统中的身份匹配策略去检索会员<br/>"
            + " 2. 此接口默认查询 MemberQueryData类定义的标准会员字段<br/>"
            + " 3. optionalFields:若发现默认查询返回字段不满足需求，可以用此参数传入想要额外查询的会员字段列表(会员身上的所有字段),自定义字段查询结果将在optionalFieldData透出<br/>"
            + " 4.默认查询生效以及冻结状态的会员，若会员失效则不返回<br/>"
            + " 5.当会员不存在时，data返回null"
    )
    @Post("/member/matchStrategy")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberQueryResult queryMemberByMatchStrategy(@Valid @Body MemberQueryByIdentifyMatchRequest request);

    @Operation(summary = "根据渠道id查询有效或者冻结的会员信息", description = "说明:"
            + " 1. 此接口默认查询 MemberQueryData类定义的标准会员字段<br/>"
            + " 2.optionalFields:若发现默认查询返回字段不满足需求，可以用此参数传入想要额外查询的会员字段列表(会员身上的所有字段),自定义字段查询结果将在optionalFieldData透出<br/>"
            + " 3.默认查询生效以及冻结状态的会员，若会员失效则不返回<br/>"
            + " 4.当会员不存在时，data返回null"
    )
    @Post("/member/channelId")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberQueryResult queryMemberInfoByChannelId(@Valid @Body MemberQueryByCustomerNoRequest request);

    @Operation(summary = "根据渠道业务字段查询渠道会员信息", description = "说明"
            + "1. customerNo、memberId以及customizedQuery不能同时为空，当多个字段有值时，优先根据customerNo查询；memberId其次，最后customizedQuery<br/>"
            + "2. 此接口默认查询 MemberBindingQueryData类定义的标准会员绑定模型字段<br/>"
            + "3. optionalFields:若发现默认查询返回字段不满足需求，可以用此参数传入想要额外查询的会员绑定模型的字段列表(会员绑定模型身上的所有字段),自定义字段查询结果将在optionalFieldData透出<br/>"
            + "4. 查询所有渠道状态的渠道会员信息,若只想要某状态的渠道会员 需要外部过滤<br/>"
            + "5. 当渠道会员不存在时，data返回null"
    )
    @Post("/member/channel")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBindingQueryResult queryChannelMembers(@Valid @Body MemberChannelQueryRequest request);

    @Operation(summary = "保存渠道会员信息", description = "1. 根据渠道id查询会员，若存在则更新；若不存在则新增,适用于保存绑定、解绑渠道记录<br/>"
            + "2. 待渠道会员记录保存完成后，将会保存行为轨迹", hidden = true)
    @Post("/member/channel/save")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult saveMemberChannel(@Valid @Body ChannelMemberSaveRequest request);

    @Operation(summary = "补充会员手机号 -明文手机号从没有到有,适用于淘宝、抖店等密文平台拿到明文手机号后补充会员手机号", hidden = true)
    @Post("/member/mobile/supplement")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberBaseResult mobileSupplement(@Valid @Body OmniMobileSupplementDto request);
}
