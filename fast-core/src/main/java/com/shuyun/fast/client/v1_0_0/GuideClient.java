package com.shuyun.fast.client.v1_0_0;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.v1_0_0.domain.GuideDetail;
import com.shuyun.fast.v1_0_0.domain.GuideList;
import com.shuyun.fast.v1_0_0.param.guide.GuideChangeParam;
import com.shuyun.fast.v1_0_0.param.guide.GuideJobAndJurisdictionShopsParam;
import com.shuyun.fast.v1_0_0.param.guide.KylinGuideBindParam;
import com.shuyun.fast.v1_0_0.result.KylinGuideResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import retrofit2.http.PUT;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Client(id = "openapi-server", path = "/openapi-server/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface GuideClient {
    @Operation(summary = "导购绑定", description = "")
    @Post("/member/external/bindGuide")
    @Api
    KylinGuideResponse<Object> guideBind(@Valid @Body KylinGuideBindParam request);

    @Operation(summary = "导购解绑", description = "")
    @Post("/member/external/unbindGuide")
    KylinGuideResponse<Object> guideUnbind(@Valid @Body KylinGuideBindParam request);

    @Operation(summary = "更换导购", description = "")
    @Post("/member/external/changeGuide")
    @Api
    KylinGuideResponse<Object> guideChange(@Valid @Body GuideChangeParam request);

    @Operation(summary = "导购列表查询", description = "")
    @Post("/guide/external/guideList")
    KylinGuideResponse<GuideList> guideList(@Parameter(description = "在职状态") @QueryValue(value = "status")String status,
                                            @Parameter(description = "门店编码") @QueryValue(value = "shopCode")@NotEmpty String shopCode);

    @Operation(summary = "导购详情查询", description = "")
    @Post("/guide/external/guideDetail")
    KylinGuideResponse<GuideDetail> guideDetail(@Parameter(description = "成员id") @QueryValue(value = "userId")String userId,
                                                @Parameter(description = "导购工号") @QueryValue(value = "employeeId")@NotEmpty String employeeId);


    @Operation(summary = "更新企业成员", description = "")
    @PUT("/guide/external/{id} ")
    @Api
    KylinGuideResponse<Object> guideExternalChange(@PathVariable("id") @NotNull Integer id,@Valid @Body Map<String,Object> request);


    @Operation(summary = "更新企业成员职位和管辖门店", description = "")
    @PUT("/guide/external/updateGuideJobAndJurisdiction")
    @Api
    KylinGuideResponse<Object> updateGuideJobAndJurisdiction(@Valid @Body GuideJobAndJurisdictionShopsParam request);

}
