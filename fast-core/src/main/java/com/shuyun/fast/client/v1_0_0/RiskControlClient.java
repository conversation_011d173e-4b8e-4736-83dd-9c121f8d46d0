package com.shuyun.fast.client.v1_0_0;

import com.shuyun.fast.v1_0_0.domain.RiskControlCheckListRequest;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;

@Client(id = "risk-control", path = "/risk-control/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface RiskControlClient {

    @Operation(summary = "加入黑名单")
    @Put("/api/checklist")
    @Consumes(MediaType.APPLICATION_JSON)
    void addBlackList(@Body RiskControlCheckListRequest request);

    @Operation(summary = "移除黑名单")
    @Delete("/api/checklist")
    @Consumes(MediaType.APPLICATION_JSON)
    void removeBlackList(@Body RiskControlCheckListRequest request);
}
