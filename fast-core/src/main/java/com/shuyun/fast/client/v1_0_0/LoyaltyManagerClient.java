package com.shuyun.fast.client.v1_0_0;

import com.shuyun.fast.loyalty.FSMPointEvent;
import com.shuyun.fast.loyalty.PCStatus;
import com.shuyun.fast.loyalty.PointSortType;
import com.shuyun.fast.loyalty.SortType;
import com.shuyun.fast.v1_0_0.param.mdm.MdmGetAmountParam;
import com.shuyun.loyalty.sdk.api.model.GradeRecordType;
import com.shuyun.loyalty.sdk.api.model.Page;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeRecordResponse;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeResponse;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRecordResponse;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberValidPointResponse;
import io.micronaut.core.convert.format.Format;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Client(id = "loyalty-manager", path = "/loyalty-manager/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface LoyaltyManagerClient {

    @Operation(summary = "获取会员等级")
    @Get("/open/grade/member")
    @Consumes(MediaType.APPLICATION_JSON)
    List<MemberGradeResponse> findMemberGradeList(@Parameter(description = "当前页,从0开始", example = "0") @QueryValue(value = "number", defaultValue = "0")Integer number,
                                                  @Parameter(description = "每页记录数 ", example = "20") @QueryValue(value = "pageSize", defaultValue = "20")Integer pageSize,
                                                  @Parameter(description = "等级体系id" ,required = true) @QueryValue(value = "gradeHierarchyId") @NotNull Long gradeHierarchyId,
                                                  @Parameter(description = "会员ID") @QueryValue(value = "memberId")String memberId,
                                                  @Parameter(description = "当前等级ID") @QueryValue(value = "currentGradeDefinitionId") @Nullable Long currentGradeDefinitionId);

    @Operation(summary = "获取会员等级记录")
    @Get("/open/grade/member/record")
    List<MemberGradeRecordResponse> findMemberGradeRecords(@Parameter(description = "当前页,从0开始", example = "0") @QueryValue(value = "number", defaultValue = "0") Integer number,
                                                           @Parameter(description = "每页记录数 ", example = "20") @QueryValue(value = "pageSize", defaultValue = "20") Integer pageSize,
                                                           @Parameter(description = "等级体系id" ,required = true) @QueryValue(value = "gradeHierarchyId") @NotNull Long gradeHierarchyId,
                                                           @Parameter(description = "会员ID") @QueryValue(value = "memberId") String memberId,
                                                           @Parameter(description = "channelType") @QueryValue(value = "channelType") @Nullable String channelType,
                                                           @Parameter(description = "排序") @QueryValue(value = "sortType") SortType sortType,
                                                           @Parameter(description = "变更类型") @QueryValue(value = "recordType") @Nullable GradeRecordType recordType,
                                                           @Parameter(description = "变更前等级ID") @QueryValue(value = "orignalGradeId") @Nullable Long orignalGradeId,
                                                           @Parameter(description = "变更后等级ID") @QueryValue(value = "currentGradeId") @Nullable Long currentGradeId,
                                                           @Format ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") @Parameter(description = "变更筛选开始时间,格式zoneddatetime") @QueryValue(value = "startTime") ZonedDateTime startTime,
                                                           @Format ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") @Parameter(description = "变更筛选结束时间,格式zoneddatetime") @QueryValue(value = "endTime") ZonedDateTime endTime);




    @Operation(summary = "分页获取会员等级记录")
    @Get("/open/grade/member/pageRecord")
    Page<MemberGradeRecordResponse> pageMemberGradeRecordList(@Parameter(description = "当前页,从0开始", example = "0") @QueryValue(value = "number", defaultValue = "0")Integer number,
                                                              @Parameter(description = "每页记录数 ", example = "20") @QueryValue(value = "pageSize", defaultValue = "20")Integer pageSize,
                                                              @Parameter(description = "等级体系id" ,required = true) @QueryValue(value = "gradeHierarchyId")Long gradeHierarchyId,
                                                              @Parameter(description = "会员ID") @QueryValue(value = "memberId")String memberId,
                                                              @Parameter(description = "channelType") @QueryValue(value = "channelType") @Nullable String channelType,
                                                              @Parameter(description = "排序") @QueryValue(value = "sortType") SortType sortType,
                                                              @Parameter(description = "变更类型") @QueryValue(value = "recordType") @Nullable GradeRecordType recordType,
                                                              @Parameter(description = "变更前等级ID") @QueryValue(value = "orignalGradeId") @Nullable Long orignalGradeId,
                                                              @Parameter(description = "变更后等级ID") @QueryValue(value = "currentGradeId") @Nullable Long currentGradeId,
                                                              @Format ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") @Parameter(description = "变更筛选开始时间,格式zoneddatetime") @QueryValue(value = "startTime") ZonedDateTime startTime,
                                                              @Format ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") @Parameter(description = "变更筛选结束时间,格式zoneddatetime") @QueryValue(value = "endTime")ZonedDateTime endTime);

    @Operation(summary = "获取会员积分记录总数")
    @Get("/open/point/member/recordCount")
    @Consumes(MediaType.APPLICATION_JSON)
    Long findMemberPointRecordsCount(@Parameter(description = "积分账户id", required = true) @QueryValue(value = "pointAccountId") @NotNull Long pointAccountId,
                                    @Parameter(description = "触发动作") @QueryValue(value = "recordType") @Nullable FSMPointEvent recordType,
                                    @Parameter(description = "批量触发动作") @QueryValue(value = "recordTypes") @Nullable List<FSMPointEvent> recordTypes,
                                    @Parameter(description = "状态") @QueryValue(value = "status") @Nullable List<PCStatus> status,
                                    @Parameter(description = "会员ID") @QueryValue(value = "memberId")String memberId,
                                    @Parameter(description = "店铺ID") @QueryValue(value = "shopId") @Nullable String shopId,
                                    @Parameter(description = "channelType") @QueryValue(value = "channelType") @Nullable String channelType,
                                    @Format ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") @Parameter(description = "变更筛选开始时间,格式zoneddatetime") @QueryValue(value = "startTime")ZonedDateTime startTime,
                                    @Format ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") @Parameter(description = "变更筛选结束时间,格式zoneddatetime") @QueryValue(value = "endTime")ZonedDateTime endTime,
                                    @Parameter(description = "原单ID") @QueryValue(value = "traceId") @Nullable String traceId,
                                    @Parameter(description = "变更方式") @QueryValue(value = "changeMode") @Nullable String changeMode);
    @Operation(summary = "获取会员积分记录")
    @Get("/open/point/member/record")
    @Consumes(MediaType.APPLICATION_JSON)
    List<MemberPointRecordResponse> findMemberPointRecords(@Parameter(description = "当前页,从0开始", example = "0") @QueryValue(value = "number", defaultValue = "0")Integer number,
                                                           @Parameter(description = "每页记录数 ", example = "20") @QueryValue(value = "pageSize", defaultValue = "20")Integer pageSize,
                                                           @Parameter(description = "积分账户id", required = true) @QueryValue(value = "pointAccountId") @NotNull Long pointAccountId,
                                                           @Parameter(description = "触发动作") @QueryValue(value = "recordType") @Nullable FSMPointEvent recordType,
                                                           @Parameter(description = "批量触发动作") @QueryValue(value = "recordTypes") @Nullable List<FSMPointEvent> recordTypes,
                                                           @Parameter(description = "状态") @QueryValue(value = "status") @Nullable List<PCStatus> status,
                                                           @Parameter(description = "会员ID") @QueryValue(value = "memberId")String memberId,
                                                           @Parameter(description = "店铺ID") @QueryValue(value = "shopId") @Nullable String shopId,
                                                           @Parameter(description = "channelType") @QueryValue(value = "channelType") @Nullable String channelType,
                                                           @Format ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") @Parameter(description = "变更筛选开始时间,格式zoneddatetime") @QueryValue(value = "startTime")ZonedDateTime startTime,
                                                           @Format ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") @Parameter(description = "变更筛选结束时间,格式zoneddatetime") @QueryValue(value = "endTime")ZonedDateTime endTime,
                                                           @Parameter(description = "原单ID") @QueryValue(value = "traceId") @Nullable String traceId,
                                                           @Parameter(description = "排序") @QueryValue(value = "sortType") @NotNull PointSortType sortType,
                                                           @Parameter(description = "变更方式") @QueryValue(value = "changeMode") @Nullable String changeMode);

    @Operation(summary = "获取即将到期积分")
    @Get("/open/point/member/valid")
    @Consumes(MediaType.APPLICATION_JSON)
    List<MemberValidPointResponse> findMemberValidPointList(@Parameter(description = "当前页,从0开始", example = "0") @QueryValue(value = "number", defaultValue = "0")Integer number,
                                                            @Parameter(description = "每页记录数 ", example = "100") @QueryValue(value = "pageSize", defaultValue = "100")Integer pageSize,
                                                            @Parameter(description = "积分账户id", required = true) @QueryValue(value = "pointAccountId") @NotNull Long pointAccountId,
                                                            @Parameter(description = "会员ID", required = true) @QueryValue(value = "memberId")@NotEmpty String memberId,
                                                            @Parameter(description = "单位:YEAR,MONTH,DAY", required = true) @QueryValue(value = "timeUnit") @NotNull TimeUnit timeUnit,
                                                            @Parameter(description = "值", required = true) @QueryValue(value = "timeValue")@NotNull TimeUnit timeValue);

    @Post("/open/point/migration/importMetaLog/{migrationId}")
    @Consumes(MediaType.APPLICATION_JSON)
    void batchImport(@PathVariable("migrationId") String migrationId,
                            @Parameter(description = "planId") Integer planId,
                            @Parameter(description = "pointId") Integer pointId,
                            @Parameter(description = "overrideHistoryPoint") Boolean overrideHistoryPoint,
                            @Parameter @Valid List<Map<String,Object>> memberPointImportRequests);

    @Post("/open/point/migration/applyMetaLog/{migrationId}")
    @Consumes(MediaType.APPLICATION_JSON)
    void calc(@PathVariable("migrationId") String migrationId,
                     @Parameter(description = "planId") Integer planId,
                     @Parameter(description = "pointId") Integer pointId);

    @Operation(summary = "查询累计消费金额")
    @Post("/open/grade/member/budget")
    @Consumes(MediaType.APPLICATION_JSON)
    List<Map<String, Object>> budget(@Valid @Body MdmGetAmountParam request);
}
