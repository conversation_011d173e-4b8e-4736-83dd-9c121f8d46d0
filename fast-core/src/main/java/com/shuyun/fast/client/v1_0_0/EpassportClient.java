package com.shuyun.fast.client.v1_0_0;

import com.shuyun.epassport.sdk.Org;
import com.shuyun.epassport.sdk.Page;
import com.shuyun.fast.v1_0_0.domain.OrgResult;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Client(id = "epassport", path = "/epassport/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface EpassportClient {
    @Operation(summary = "组织创建")
    @Post("/admin/orgs")
    @Consumes(MediaType.APPLICATION_JSON)
    OrgResult create(@Valid @Body Org org);

    @Operation(summary = "组织更新")
    @Put("/admin/orgs/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    void update(@PathVariable("id") @NotNull Long id,
                @Valid @Body Org org);
    @Operation(summary = "组织删除")
    @Delete("/admin/orgs/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    void delete(@PathVariable("id") @NotNull Long id);

    @Operation(summary = "分页查询组织信息")
    @Get("/admin/orgs/page")
    @Consumes(MediaType.APPLICATION_JSON)
    Page<OrgResult> list(@QueryValue(value = "code") String code,
                         @Nullable  @QueryValue(value = "name") String name);
}
