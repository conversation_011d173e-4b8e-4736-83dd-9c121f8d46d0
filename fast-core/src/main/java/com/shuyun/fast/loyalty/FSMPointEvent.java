package com.shuyun.fast.loyalty;

import javax.validation.constraints.NotNull;

public enum FSMPointEvent {

    SEND("立即发放"),
    DELAY_SEND("延迟发放"),
    EXPIRE("过期"),
    FREEZE("冻结"),
    UNFREEZE("取消冻结"),
    DEDUCT("扣减"),
    ABOLISH("作废"),
    TIMER("定时"),
    RECALCULATE("废弃重算"),
    SPECIAL_DEDUCT("特殊扣除"),
    SPECIAL_FREEZE("特殊冻结"),
    SPECIAL_UNFREEZE("特殊解冻"),
    SPECIAL_ABOLISH("特殊废弃"),
    MANUAL_ABOLISH("手动废弃"),
    OPEN_FREEZE("接口冻结"),
    OPEN_UNFREEZE("接口解冻"),
    REVERSE_SEND("反向接口发放"),
    REVERSE_DEDUCT("反向接口扣减");

    @NotNull
    private final String desc;

    FSMPointEvent(String desc) {
        this.desc = desc;
    }

    @NotNull
    public final String getDesc() {
        return this.desc;
    }
}
