package com.shuyun.fast.entity;

import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import io.micronaut.data.annotation.MappedProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;

@Data
@MappedEntity("t_biz_cache")
public class BizCache {

    public static String BENEFIT = "benefit";
    public static String GRADE = "grade";
    public static String POINT = "point";
    public static String MBSP = "mbsp";
    public static String SELECTOR = "selector";
    public static String MDM = "mdm";
    public static String TRADE = "trade";
    public static String TAG = "tag";

    @Id
    @GeneratedValue(GeneratedValue.Type.AUTO)
    private Integer id;

    @NotNull
    @MappedProperty(value = "tenantId")
    private String tenantId;
    @NotNull
    @MappedProperty(value = "bizCode")
    private String bizCode;
    @NotNull
    @MappedProperty(value = "cacheType")
    private String cacheType;
    @NotNull
    private String value;
    @MappedProperty(value = "createTime")
    private Timestamp createTime;
    @MappedProperty(value = "updateTime")
    private Timestamp updateTime;

}
