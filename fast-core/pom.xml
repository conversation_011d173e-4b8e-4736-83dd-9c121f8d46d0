<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shuyun</groupId>
        <artifactId>fast-integration</artifactId>
        <version>0.1.0</version>
    </parent>

    <artifactId>fast-core</artifactId>

    <properties>
        <redisson.version>3.23.3</redisson.version>
        <dataapi.version>1.40.0.RELEASE</dataapi.version>
        <es.eventservice.version>1.9.6.RC3</es.eventservice.version>
        <okhttp.version>4.9.3</okhttp.version>
        <fastjson.version>1.2.28</fastjson.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>

        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>fast-open-sdk</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>javax.ws.rs-api</artifactId>
                    <groupId>javax.ws.rs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dm-dataapi-sdk</artifactId>
                    <groupId>com.shuyun.dm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dm-sdk-common</artifactId>
                    <groupId>com.shuyun.dm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.micronaut.openapi</groupId>
            <artifactId>micronaut-openapi</artifactId>
            <version>${micronaut.openapi.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-nop</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-client</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-server-netty</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>io.micronaut.test</groupId>
            <artifactId>micronaut-test-junit5</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>io.micronaut.cache</groupId>
            <artifactId>micronaut-cache-caffeine</artifactId>
            <scope>compile</scope>
        </dependency>


        <dependency>
            <groupId>io.micronaut.kafka</groupId>
            <artifactId>micronaut-kafka</artifactId>
            <scope>compile</scope>
        </dependency>

        <!--jdbc相关start-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.data</groupId>
            <artifactId>micronaut-data-jdbc</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>io.micronaut.data</groupId>
            <artifactId>micronaut-data-spring</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>io.micronaut.sql</groupId>
                    <artifactId>micronaut-hibernate-jpa-spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.micronaut.sql</groupId>
            <artifactId>micronaut-jdbc-hikari</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>io.micronaut.flyway</groupId>
            <artifactId>micronaut-flyway</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-mysql</artifactId>
            <scope>compile</scope>
        </dependency>
        <!--jdbc相关end-->

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redisson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.dm</groupId>
            <artifactId>dm-api</artifactId>
            <version>${dataapi.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.websocket</groupId>
            <artifactId>javax.websocket-api</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>javax.websocket</groupId>
            <artifactId>javax.websocket-client-api</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.dm</groupId>
            <artifactId>dm-dataapi-sdk</artifactId>
            <version>${dataapi.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun.lite.module</groupId>
                    <artifactId>lite-passport-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>discovery-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>discovery-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>client-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>client-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>configuration-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>configuration-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>joda-time</artifactId>
                    <groupId>joda-time</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuyun.es</groupId>
            <artifactId>es-eventservice-sdk</artifactId>
            <version>${es.eventservice.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>lite-sdk-core</artifactId>
                    <groupId>com.shuyun.lite.module</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lite-passport-sdk</artifactId>
                    <groupId>com.shuyun.lite.module</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>discovery-client</artifactId>
                    <groupId>com.shuyun.spectrum</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lite-base</artifactId>
                    <groupId>com.shuyun.lite.module</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.obg</groupId>
            <artifactId>sdk</artifactId>
            <version>3.8.6</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin.security</groupId>
            <artifactId>kylin-security-sdk</artifactId>
            <version>1.1.0.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.micronaut.build</groupId>
                <artifactId>micronaut-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <useIncrementalCompilation>true</useIncrementalCompilation>
                    <annotationProcessorPaths combine.self="override">
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut</groupId>
                            <artifactId>micronaut-inject-java</artifactId>
                            <version>${micronaut.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut</groupId>
                            <artifactId>micronaut-http-validation</artifactId>
                            <version>${micronaut.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut.data</groupId>
                            <artifactId>micronaut-data-processor</artifactId>
                            <version>${micronaut.data.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut.openapi</groupId>
                            <artifactId>micronaut-openapi</artifactId>
                            <version>${micronaut.openapi.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-Amicronaut.processing.group=com.shuyun.fast</arg>
                        <arg>-Amicronaut.processing.module=fast-core</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>