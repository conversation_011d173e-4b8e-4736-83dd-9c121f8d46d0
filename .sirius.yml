language: java
jdk:
  - oraclejdk11
docker:
  - docker1.12
build:
  app_version: "mvn -q -Dexec.executable='echo' -Dexec.args='${projects.version}' --non-recursive org.codehaus.mojo:exec-maven-plugin:1.3.1:exec"
  script:
    - mvn clean install -Dmaven.test.skip=true -Pprod
package:
  - type: docker
    docker_file: fast-service/Dockerfile
    registry_uri: hub.shuyun.com
    image_name: fast/fast-service
    image_tag: ${APP_VERSION}-${TIMESTAMP}
  - type: docker
    docker_file: fast-job/Dockerfile
    registry_uri: hub.shuyun.com
    image_name: fast/fast-job
    image_tag: ${APP_VERSION}-${TIMESTAMP}
  - type: docker
    docker_file: fast-event/Dockerfile
    registry_uri: hub.shuyun.com
    image_name: fast/fast-event
    image_tag: ${APP_VERSION}-${TIMESTAMP}
compose:
  - type: service
    service_file: fast-service/.service.yml
  - type: service
    service_file: fast-job/.service.yml
  - type: service
    service_file: fast-event/.service.yml