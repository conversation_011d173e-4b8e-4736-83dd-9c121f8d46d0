package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.domain.Grade;
import com.shuyun.fast.v1_0_0.domain.GradeRecord;
import com.shuyun.fast.v1_0_0.param.grade.GradeGetParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeRecordsGetParam;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.PageResult;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;

@Tag(name = "等级场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/grade")
@Introspected
public class GradeResource {


    private final ApiHandlerRouter handlerRouter;

    public GradeResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }

    @Operation(summary = "等级查询")
    @Post("/get")
    @Api(name = ApiTags.API_NAME_GRADE_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Grade> get(@Valid @Body GradeGetParam param){
        Grade result = (Grade)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "明细查询")
    @Post("/records/get")
    @Api(name = ApiTags.API_NAME_GRADE_RECORDS_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<PageResult<GradeRecord>> recordsGet(@Valid @Body GradeRecordsGetParam param){
        PageResult<GradeRecord> result = (PageResult<GradeRecord>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }
}
