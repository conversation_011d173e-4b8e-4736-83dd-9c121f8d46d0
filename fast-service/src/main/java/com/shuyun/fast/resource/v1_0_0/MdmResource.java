package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.domain.MdmShop;
import com.shuyun.fast.v1_0_0.param.mdm.*;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.validation.Valid;

@Tag(name = "mdm场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/mdm")
@Introspected
public class MdmResource {

    private final ApiHandlerRouter handlerRouter;

    public MdmResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }

    @Operation(summary = "商品同步")
    @Post("/product/sync")
    @Api(name = ApiTags.API_NAME_MDM_PRODUCT_SYNC)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> productSync(@Valid @Body MdmProductSyncParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "门店同步")
    @Post("/shop/sync")
    @Api(name = ApiTags.API_NAME_MDM_SHOP_SYNC)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> shopSync(@Valid @Body MdmShopSyncParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "门店列表")
    @Post("/shop/list")
    @Api(name = ApiTags.API_NAME_MDM_SHOP_LIST)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<PageResult<MdmShop>> shopList(@Valid @Body MdmShopListParam param){
        PageResult<MdmShop> result = (PageResult<MdmShop>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }


    @Operation(summary = "组织结构同步")
    @Post("/org/sync")
    @Api(name = ApiTags.API_NAME_MDM_ORG_SYNC)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> orgSync(@Valid @Body MdmOrgSyncParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "有赞商品信息同步")
    @Post("/youzan/product/sync")
    @Api(name = ApiTags.API_NAME_YOUZAN_PRODUCT_SYNC)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> youzanProductSync(@Valid @Body YouzanProductSyncParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }
}
