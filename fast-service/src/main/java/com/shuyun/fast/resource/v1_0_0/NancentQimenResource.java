package com.shuyun.fast.resource.v1_0_0;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.base.NancentResult;
import com.shuyun.fast.service.v1_0_0.NancentService;
import com.shuyun.fast.v1_0_0.param.nancent.DataJson;
import com.shuyun.fast.v1_0_0.param.nancent.Member;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @title NancentQimenResource
 * @description 南讯奇门数据传输接口
 * @create 2024/11/25 15:38
 */

@Tag(name = "南讯奇门数据对接")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/nancent")
@Introspected
@Slf4j
public class NancentQimenResource {
    NancentService nancentService;
    public NancentQimenResource(NancentService nancentService) {
        this.nancentService = nancentService;
    }


    @Operation(summary = "奇门会员数据")
    @Post("/member")
    public NancentResult memberPost(@Valid @Body DataJson param){
        log.info("保存南讯奇门数据Start:{}",param);
        String dataType = param.getDataType();
        String mesaage = param.getMessage();
        JSONObject dataMessage = JSONObject.parseObject(mesaage);
        try{
            if("CUSTOMER".equals(dataType)){
                nancentService.nancentTaobaoMemberSave(dataMessage);
            }else if("TRADE".equals(dataType)){
                nancentService.nancentTaobaoOrderSave(dataMessage);
            }else if("REFUND".equals(dataType)){
                nancentService.nancentTaobaoRefundSave(dataMessage);
            }
            return NancentResult.success();
        }catch (Exception e){
            log.error("保存南讯淘宝会员出现异常:",e);
            return NancentResult.fail("数据处理失败");
        }

    }

   /* @Operation(summary = "订单")
    @Post("/order")
    public NancentResult orderPost(@Body Map<String,Object> param){
        log.info("保存南讯淘宝订单Start:{}",param);
        try{
            nancentService.nancentTaobaoOrderSave(param);
            return NancentResult.success();
        }catch (Exception e){
            log.error("保存南讯淘宝订单出现异常:",e);
            return NancentResult.fail("数据处理失败");
        }

    }*/

}
