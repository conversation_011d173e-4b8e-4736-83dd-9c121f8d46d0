package com.shuyun.fast.resource.v1_0_0;


import com.shuyun.fast.service.v1_0_0.BenefitSpiService;
import com.shuyun.fast.v1_0_0.domain.DeliveryChannelSPI;
import com.shuyun.fast.v1_0_0.domain.GiftSpecificationsSPI;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @title BenefitSpiResource
 * @description
 * @create 2025/8/13 14:04
 */
@Tag(name = "权益spi")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/benefitSpi")
@Introspected
@Slf4j
public class BenefitSpiResource {

    @Inject
    private BenefitSpiService benefitSpiService;

    @Operation(summary = "礼品SPI资源接口")
    @Post("/gift")
    public List<GiftSpecificationsSPI> benefitGift(){
        log.info("benefitGift请求");
        try {
            List<GiftSpecificationsSPI> giftSpecificationsSPIs = benefitSpiService.getGiftSpecifications();
            return giftSpecificationsSPIs;
        }catch (Exception e){
            log.error("礼品SPI资源接口出现异常：",e);
        }
        return null;
    }


    @Operation(summary = "发货渠道SPI资源接口")
    @Post("/channel")
    public List<DeliveryChannelSPI> benefitChannel(){
        log.info("benefitChannel请求");
        try {
            List<DeliveryChannelSPI> channelSPIS = benefitSpiService.getChannelSpi();
            return channelSPIS;
        }catch (Exception e){
            log.error("发货渠道SPI资源接口：",e);
        }
        return null;
    }
}
