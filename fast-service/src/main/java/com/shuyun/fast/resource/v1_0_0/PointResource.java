package com.shuyun.fast.resource.v1_0_0;


import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.domain.Point;
import com.shuyun.fast.v1_0_0.domain.PointRecord;
import com.shuyun.fast.v1_0_0.param.point.*;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.validation.Valid;

@Tag(name = "积分场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/point")
@Introspected
public class PointResource {

    private final ApiHandlerRouter handlerRouter;

    public PointResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }


    @Operation(summary = "积分查询")
    @Post("/get")
    @Api(name = ApiTags.API_NAME_POINT_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Point> get(@Valid @Body PointGetParam param){
        Point result = (Point)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "明细查询", description = "本接口响应参数只返回总条数,不返回总页数")
    @Post("/records/get")
    @Api(name = ApiTags.API_NAME_POINT_RECORDS_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<PageResult<PointRecord>> recordsGet(@Valid @Body PointRecordsGetParam param){
        PageResult<PointRecord> result = (PageResult<PointRecord>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "积分变更")
    @Post("/modify")
    @Api(name = ApiTags.API_NAME_POINT_MODIFY)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> modify(@Valid @Body PointModifyParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "积分冻结")
    @Post("/freeze")
    @Api(name = ApiTags.API_NAME_POINT_FREEZE)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> freeze(@Valid @Body PointFreezeParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "消耗已冻结积分")
    @Post("/freeze/deduct")
    @Api(name = ApiTags.API_NAME_POINT_FREEZE_DEDUCT)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> freezeDeduct(@Valid @Body PointFreezeDeductParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "积分解冻")
    @Post("/unfreeze")
    @Api(name = ApiTags.API_NAME_POINT_UNFREEZE)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> unfreeze(@Valid @Body PointUnfreezeParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "积分交易撤销")
    @Post("/revert")
    @Api(name = ApiTags.API_NAME_POINT_REVERT)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> revert(@Valid @Body PointRevertParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

}
