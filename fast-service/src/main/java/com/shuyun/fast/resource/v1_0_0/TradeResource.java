package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.domain.TradeMainOrder;
import com.shuyun.fast.v1_0_0.param.trade.TradeGetParam;
import com.shuyun.fast.v1_0_0.param.trade.TradeOrderSyncParam;
import com.shuyun.fast.v1_0_0.param.trade.TradeRefundSyncParam;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.validation.Valid;

@Tag(name = "销售场景")
@ExecuteOn("blocking")
// @Controller("/v1/0.0/api/trade")
@Introspected
public class TradeResource {

    private final ApiHandlerRouter handlerRouter;

    public TradeResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }

    @Operation(summary = "订单同步")
    // @Post("/order/sync")
    @Api(name = ApiTags.API_NAME_TRADE_ORDER_SYNC)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> orderSync(@Valid @Body TradeOrderSyncParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "退单同步")
    // @Post("/refund/sync")
    @Api(name = ApiTags.API_NAME_TRADE_REFUND_SYNC)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> refundSync(@Valid @Body TradeRefundSyncParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "订单查询")
    // @Post("/get")
    @Api(name = ApiTags.API_NAME_TRADE_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<PageResult<TradeMainOrder>> get(@Valid @Body TradeGetParam param){
        PageResult<TradeMainOrder> result = (PageResult<TradeMainOrder>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }
}
