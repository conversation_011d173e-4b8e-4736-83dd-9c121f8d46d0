package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.domain.Coupon;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.domain.Grade;
import com.shuyun.fast.v1_0_0.param.coupon.*;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.v1_0_0.result.CouponDiscountCalcResult;
import com.shuyun.fast.v1_0_0.result.CouponGetResult;
import com.shuyun.ticket.benefit.vo.Order;
import com.shuyun.ticket.benefit.vo.Page;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import com.shuyun.fast.base.ApiTags;
import java.util.List;

@Tag(name = "卡券场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/coupon")
@Introspected
public class CouponResource {


    private final ApiHandlerRouter handlerRouter;

    public CouponResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }

    @Operation(summary = "卡券发放")
    @Post("/grant")
    @Api(name = ApiTags.API_NAME_COUPON_GRANT)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<Coupon>> grant(@Valid @Body CouponGrantParam param){
        List<Coupon> result = (List<Coupon>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "卡券作废")
    @Post("/grant/repeal")
    @Api(name = ApiTags.API_NAME_COUPON_GRANT_REPEAL)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<Coupon>> grantRepeal(@Valid @Body CouponGrantRepealParam param){
        List<Coupon> result = (List<Coupon>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "优惠券列表")
    @Post("/list")
    @Api(name = ApiTags.API_NAME_COUPON_LIST)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<CouponGetResult>> list(@Valid @Body CouponListParam param){
        List<CouponGetResult> result = (List<CouponGetResult>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "可用券列表")
    @Post("/available/list")
    @Api(name = ApiTags.API_NAME_COUPON_AVAILABLE_LIST)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<CouponGetResult>> availableList(@Valid @Body CouponAvailableListParam param){
        List<CouponGetResult> result = (List<CouponGetResult>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "优惠计算")
    @Post("/discount/calc")
    @Api(name = ApiTags.API_NAME_COUPON_DISCOUNT_CALC)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<CouponDiscountCalcResult> discountCalc(@Valid @Body CouponDiscountCalcParam param){
        CouponDiscountCalcResult result = (CouponDiscountCalcResult)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "锁定券")
    @Post("/lock")
    @Api(name = ApiTags.API_NAME_COUPON_LOCK)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<Coupon>> lock(@Valid @Body CouponLockParam param){
        List<Coupon> result = (List<Coupon>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "解锁券")
    @Post("/unlock")
    @Api(name = ApiTags.API_NAME_COUPON_UNLOCK)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<Coupon>> unlock(@Valid @Body CouponUnlockParam param){
        List<Coupon> result = (List<Coupon>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "券查询")
    @Post("/get")
    @Api(name = ApiTags.API_NAME_COUPON_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<CouponGetResult> get(@Valid @Body CouponGetParam param){
        CouponGetResult result = (CouponGetResult)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "券项目列表")
    @Post("/project/list")
    @Api(name = ApiTags.API_NAME_COUPON_PROJECT_LIST)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Page<CouponProject>> projectList(@Valid @Body CouponProjectListParam param){
        Page<CouponProject> result = (Page<CouponProject>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "券项目查询")
    @Post("/project/get")
    @Api(name = ApiTags.API_NAME_COUPON_PROJECT_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<CouponProject> projectGet(@Valid @Body CouponProjectGetParam param){
        CouponProject result = (CouponProject)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "核销")
    @Post("/consume")
    @Api(name = ApiTags.API_NAME_COUPON_CONSUME)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Order> consume(@Valid @Body CouponConsumeParam param){
        Order result = (Order)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "反核销")
    @Post("/consume/repeal")
    @Api(name = ApiTags.API_NAME_COUPON_CONSUME_REPEAL)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> consumeRepeal(@Valid @Body CouponConsumeRepealParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "转赠")
    @Post("/transfer")
    @Api(name = ApiTags.API_NAME_COUPON_TRANSFER)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> transfer(@Valid @Body CouponTransferParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "受赠")
    @Post("/receive")
    @Api(name = ApiTags.API_NAME_COUPON_RECEIVE)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> receive(@Valid @Body CouponReceiveParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "退回")
    @Post("/return")
    @Api(name = ApiTags.API_NAME_COUPON_RETURN)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> returns(@Valid @Body CouponReturnParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }
}
