package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.param.department.DepartmentInitOrgParam;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;

@Tag(name = "部门管理")
@ExecuteOn("blocking")
@Controller("/v1/0.0/department")
@Slf4j
public class DepartmentResource {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Api(name = ApiTags.API_NAME_DEPARTMENT_INIT_ORG)
    @Post("/initOrg")
    public ApiResult<Void> initOrg(@Valid @Body DepartmentInitOrgParam param) {
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }
}
