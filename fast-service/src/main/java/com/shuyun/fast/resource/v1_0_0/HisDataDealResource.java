package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.factory.ThreadPoolExecutorFactory;
import com.shuyun.fast.service.v1_0_0.HisDataDealService;
import com.shuyun.fast.v1_0_0.param.history.PrepareRequest;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @title HisDataDealResource
 * @description 历史数据处理
 * @create 2024/12/18 18:13
 */
@Tag(name = "历史数据处理")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/hisData")
@Introspected
@Slf4j
public class HisDataDealResource {
    @Inject
    private HisDataDealService hisDataDealService;

    /**
     * 历史积分导入
     * @param request
     * @return
     */
    @Operation(summary = "历史积分明细导入")
    @Post("/member/pointImport")
    public ApiResult<Void> mmtPointImport(@Body PrepareRequest request){
        log.info("/member/pointImport:{}", request);
        try {
            hisDataDealService.pointImport(request);
        }catch (Exception e){
            log.error("历史积分明细导入出现异常：",e);
        }
        return ApiResult.success();
    }
    /**
     * 历史积分应用
     * @param request
     * @return
     */
    @Operation(summary = "历史积分明细启用")
    @Post("/member/pointUse")
    public ApiResult<String> mmtPointUse(@Body PrepareRequest request){
        log.info("/MMT/member/pointUse:{}", request);
        try{
            hisDataDealService.pointUse(request);
        } catch(Exception e){
            log.error("历史积分明细应用出现异常：{}",e);
        }
        return ApiResult.success();
    }


}
