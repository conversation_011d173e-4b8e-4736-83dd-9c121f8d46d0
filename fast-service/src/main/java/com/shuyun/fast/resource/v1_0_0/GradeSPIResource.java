package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.service.v1_0_0.GradeService;
import com.shuyun.fast.v1_0_0.domain.MemberNextGradeAmount;
import com.shuyun.fast.v1_0_0.param.member.MemberGetNextAmountParam;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;

import javax.validation.constraints.NotNull;
import java.util.Optional;

@Tag(name = "等级场景SPI")
@ExecuteOn("blocking")
@Controller("/v1/0.0/spi/grade")
@Introspected
public class GradeSPIResource {

    @Inject
    private GradeService gradeService;

    @Operation(summary = "距离下一等级消费金额")
    @Get("/nextGradeAmount")
    public MemberNextGradeAmount nextGradeAmount(@Parameter(description = "会员ID", example = "P202406201GXQST")
                                                 @QueryValue @NotNull String memberId,
                                                 @Parameter(description = "等级业务类型,不传,默认为: GRADE", example = "GRADE")
                                                 @QueryValue(defaultValue = "GRADE") String gradeBizType,
                                                 @Parameter(description = "等级体系id,不传,默认从配置文件获取", example = "60004")
                                                 @QueryValue Optional<Long> gradeHierarchyId) {
        return gradeService.nextGradeAmount(new MemberGetNextAmountParam(memberId, gradeBizType, gradeHierarchyId.orElse(null)));
    }
}
