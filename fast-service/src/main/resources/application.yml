micronaut:
  application:
    name: fast-service
  server:
    port: 8080
    context-path: /fast-service
  netty:
    event-loops:
      default:
        num-threads: 32
      client:
        num-threads: 32
  http:
    client:
      ssl:
        insecure-trust-all-certificates: true
      event-loop-group: client
      connect-timeout: 5s
      read-timeout: 30s
      connection-pool-idle-timeout: 60s
      pool:
        enabled: true
        max-connections: 160
        max-pending-acquires: 16
        acquire-timeout: 50ms
  executors:
    blocking:
      type: fixed
      number-of-threads: 128
  caches:
    firstBizCache:
      charset: UTF-8
      maximum-size: 1000
#      expire-after-access: 10s
    secondBizCache:
      charset: UTF-8
      maximum-size: 1000
#      expire-after-access: 10s
    firstProjectCache:
      charset: UTF-8
      maximum-size: 5000
#      expire-after-access: 10s
    secondProjectCache:
      charset: UTF-8
      maximum-size: 5000
#      expire-after-access: 10s
    selectorCache:
      charset: UTF-8
      maximum-size: 10000
#      expire-after-access: 10s
  openapi:
    views:
      #编译期生成swagger-ui所需文件
      spec: swagger-ui.enabled=true
#    json:
#      format: true #为false生成yml文件,为true生成json文件
  router:
    static-resources:
      swagger:
        paths: classpath:META-INF/swagger
        mapping: /swagger/**
      swagger-ui:
        paths: classpath:META-INF/swagger/views/swagger-ui
        mapping: /swagger-ui/**

logger:
  config: logback.xml

jackson:
  serializationInclusion: NON_NULL
  deserialization:
    FAIL_ON_UNKNOWN_PROPERTIES: false