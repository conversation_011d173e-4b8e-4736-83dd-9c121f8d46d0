datasources:
  default:
    dialect: MYSQL
    connectionInitSql: set names utf8mb4;
    jdbcUrl: jdbc:mysql://${database.host}:${database.port}/${system.tenant}_fast_service?useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&serverTimezone=Asia/Shanghai
    username: ${database.username}
    password: ${database.password}
    driverClassName: com.mysql.cj.jdbc.Driver
    maximum-pool-size: 50
    minIdle: 15
    connectionTimeout: 60000
    keepaliveTime: 60000

flyway:
  datasources:
    default:
      enabled: true
      locations:
        - classpath:/db/migration

#swagger-ui:
#  enabled: true
kafka:
  bootstrap:
    servers: ${kafka.address}

fast:
  redis:
    enable: true
    ssl: false
    address: redis://${redis.address}
    password: ${redis.password}
    poolSize: 64
    database: 0
    nettyThread: 64

passport:
  enable: true
  clientId: fast-event
  clientName: fast-event

member:
  ebrand:
    #    enable: {fast.member.ebrand.enable: false}
    enable: false

benefit:
  enable: true
#  enable: {fast.benefit.ebrand.enable}
  consumer:
    group: fast-event
  selector:
    refresh:
      rate: 2h
      delay: 20m

snowflake:
  enable: true

youzan:
  baseUrl: ${fast.youzan.baseUrl}
  clientId: ${fast.youzan.clientId}
  clientSecret: ${fast.youzan.clientSecret}
  specialProductId: ${fast.youzan.specialProductId:3746893359}

bojun:
  interface:
    url: ${fast.bojun.interface.url}
    userName: ${fast.bojun.interface.userName}
    userKey: ${fast.bojun.interface.userKey}
    requestSign: ${fast.bojun.interface.requestSign}

risk:
  group:
    employee: ${fast.risk.group.employee}