package com.shuyun.fast.youzan.coupon.request;

import lombok.Data;

@Data
public class CardGenRule {

    // ⾯额⽣成规则类型 必传
    // 1:固定值==>value字段（默认值）  (例如：兑换券)==> value==0
    // 2:指定值==>指定范围为 minValue~maxValue 之间
    // 3:范围随机==>随机范围为 minValue~maxValue 之间
    // 4:⽆指定值
    private Integer voucherValueGenerateType;
    // 优惠⾯额（单位：分）, 当折扣券时， 8折，value传80
    private Long value;
    // 随机券⾯额范围下限（单位：分）
    private Long minValue;
    // 随机券⾯额范围上限（单位：分）
    private Long maxValue;
    // 有效期⽣成类型 1：绝对时间  (absoluteValidStartTime ~ absoluteValidEndTime)
    // 2：相对时间（从 relativeValidTimeEffectInterval 天开始，relativeValidTimeDuration天内有效），
    // 3表⽰⾃然⽉(和 relativeValidTimeDuration 可以表⽰x个⾃然⽉)，
    // 4表⽰当周，
    // 5表⽰当⽉，
    // 6表⽰当年
    private Integer validTimeGenerateType;
    // 绝对有效期开始时间，格式：yyyyMM-dd HH:mm:ss
    private String absoluteValidStartTime;
    // 绝对有效期结束时间，格式：yyyyMM-dd HH:mm:ss
    private String absoluteValidEndTime;
    // 相对有效期开始⽣效时间间隔 （天），编辑时不可改⼩，可以改⼤
    private Integer relativeValidTimeBeginInterval;
    // validTimeGenerateType = 2 时，代表相对有效期持续时间（天），编辑时不可改⼩，可以改⼤。
    // validTimeGenerateType = 3，这⾥表⽰x个⾃然⽉
    private Integer relativeValidTimeDuration;
}
