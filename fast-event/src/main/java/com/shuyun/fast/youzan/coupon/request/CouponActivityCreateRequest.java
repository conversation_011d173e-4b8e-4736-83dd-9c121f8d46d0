package com.shuyun.fast.youzan.coupon.request;

import lombok.Data;

@Data
public class CouponActivityCreateRequest {

    // 外部券活动ID 必传
    private String outActivityId;
    // 活动标题 必传
    private String title;
    // 类型，1-普通优惠券 2-员⼯内购券  必传
    private Integer type;
    // 活动类型   7:满减/折扣; 13:兑换  必传
    private Integer activityType;
    // 活动描述 非必传
    private String description;
    // 活动备注 非必传
    private String remark;
    // 允许发放总量 必传
    private Long budgetSendTotalQty;
    // 是否允许分享活动(默认为false)  非必传
    private Boolean isSharable;
    // 是否允许转增(默认为false)  非必传
    private Boolean isHandsel;
    // 活动制券规则
    private CreateCardGenRule cardGenRule;
    // 活动核销规则
    private CardUsingRule cardUsingRule;
    // 活动优惠规则(兑换券需要)
    private PreferentialRule preferentialRule;
}
