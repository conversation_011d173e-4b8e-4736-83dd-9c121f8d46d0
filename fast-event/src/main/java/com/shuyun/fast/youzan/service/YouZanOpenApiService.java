package com.shuyun.fast.youzan.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shuyun.fast.youzan.config.YouZanOpenApiProperties;
import com.shuyun.fast.youzan.constant.YouZanOpenApiPathTags;
import com.shuyun.fast.youzan.coupon.request.*;
import com.shuyun.fast.youzan.coupon.response.YouZanBaseResponse;
import com.shuyun.fast.youzan.coupon.response.YouZanItemGroupResponse;
import com.shuyun.fast.youzan.coupon.response.YouZanTokenResponse;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class YouZanOpenApiService {

    @Inject
    private YouZanOpenApiProperties youZanOpenApiProperties;

    @Inject
    private RedissonClient redissonClient;

    public Long couponActivityCreate(CouponActivityCreateRequest createParam) {
        log.info("有赞优惠券活动创建: projectId:{} param:{}", createParam.getOutActivityId(), JSONObject.toJSONString(createParam));
        String token = getClientToken();
        if (StringUtils.isNotBlank(token)) {
            OkHttpClient client = new OkHttpClient();
            MediaType mediaType = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSONObject.toJSONString(createParam), mediaType);
            Request request = new Request.Builder()
                    .url(youZanOpenApiProperties.getBaseUrl() + YouZanOpenApiPathTags.YOUZAN_POST_COUPON_ACTIVITY_CREATE)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("ClientToken", token)
                    .build();
            try {
                log.info("卡券项目:{}远程同步创建有赞优惠券活动开始......", createParam.getOutActivityId());
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String respBody = response.body().string();
                    log.info("卡券项目:{}同步创建有赞优惠券活动接口成功响应,response:{}", createParam.getOutActivityId(), respBody);
                    YouZanBaseResponse<Long> result = JSONObject.parseObject(respBody, new TypeReference<YouZanBaseResponse<Long>>(){});
                    if(result.getSuccess()){
                        //log.info("卡券项目:{}同步创建有赞优惠券活动成功,有赞券活动ID:{}.", createParam.getOutActivityId(), result.getData());
                        return result.getData();
                    } else {
                        log.warn("卡券项目:{}同步创建有赞优惠券活动失败,有赞接口返回错误信息:{}", createParam.getOutActivityId(), JSONObject.toJSONString(result));
                    }
                } else {
                    log.warn("卡券项目:{}同步创建有赞优惠券活动失败,接口调用失败,response:{}", createParam.getOutActivityId(), JSONObject.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("卡券项目:{}同步创建有赞优惠券活动失败,异常信息:{}", createParam.getOutActivityId(), e.getMessage());
            }
        } else {
            log.warn("卡券项目:{}同步创建有赞优惠券活动失败,获取clientToken失败!", createParam.getOutActivityId());
        }
        return null;
    }

    public String couponActivityUpdate(CouponActivityUpdateRequest updateParam) {
        log.info("有赞优惠券活动修改: projectId:{} param::{}", updateParam.getOutActivityId(), JSONObject.toJSONString(updateParam));
        String token = getClientToken();
        if (StringUtils.isNotBlank(token)) {
            OkHttpClient client = new OkHttpClient();
            MediaType mediaType = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSONObject.toJSONString(updateParam), mediaType);
            Request request = new Request.Builder()
                    .url(youZanOpenApiProperties.getBaseUrl() + YouZanOpenApiPathTags.YOUZAN_POST_COUPON_ACTIVITY_UPDATE)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("ClientToken", token)
                    .build();
            try {
                log.info("卡券项目:{}远程同步编辑有赞优惠券活动开始......", updateParam.getOutActivityId());
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String respBody = response.body().string();
                    log.info("卡券项目:{}同步编辑有赞优惠券活动接口成功响应,response:{}", updateParam.getOutActivityId(), respBody);
                    YouZanBaseResponse result = JSONObject.parseObject(respBody, YouZanBaseResponse.class);
                    if(result.getSuccess()){
                        //log.info("卡券项目:{}同步编辑有赞优惠券活动成功!", updateParam.getOutActivityId());
                        return "SUCCESS";
                    } else {
                        log.warn("卡券项目:{}同步编辑有赞优惠券活动失败,接口返回错误信息:{}", updateParam.getOutActivityId(), JSONObject.toJSONString(result));
                    }
                } else {
                    log.warn("卡券项目:{}同步编辑有赞优惠券活动失败,接口调用失败,response:{}", updateParam.getOutActivityId(), JSONObject.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("卡券项目:{}同步编辑有赞优惠券活动失败,异常信息:{}", updateParam.getOutActivityId(), e.getMessage());
            }
        } else {
            log.warn("卡券项目:{}同步编辑有赞优惠券活动失败,获取clientToken失败!", updateParam.getOutActivityId());
        }
        return null;
    }

    public String couponActivityInvalid(CouponActivityInvalidRequest invalidParam) {
        log.info("有赞优惠券活动失效入参: projectId:{} param:{}", invalidParam.getOutActivityId(), JSONObject.toJSONString(invalidParam));
        String token = getClientToken();
        if (StringUtils.isNotBlank(token)) {
            OkHttpClient client = new OkHttpClient();
            MediaType mediaType = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSONObject.toJSONString(invalidParam), mediaType);
            Request request = new Request.Builder()
                    .url(youZanOpenApiProperties.getBaseUrl() + YouZanOpenApiPathTags.YOUZAN_POST_COUPON_ACTIVITY_INVALID)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("ClientToken", token)
                    .build();
            try {
                log.info("卡券项目:{}远程同步失效有赞优惠券活动开始......", invalidParam.getOutActivityId());
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String respBody = response.body().string();
                    log.info("卡券项目:{}同步失效有赞优惠券活动接口成功响应,response:{}", invalidParam.getOutActivityId(), respBody);
                    YouZanBaseResponse result = JSONObject.parseObject(respBody, YouZanBaseResponse.class);
                    if(result.getSuccess()){
                        //log.info("卡券项目:{}同步失效有赞优惠券活动成功!", invalidParam.getOutActivityId());
                        return "SUCCESS";
                    } else {
                        log.warn("卡券项目:{}同步失效有赞优惠券活动失败,接口返回错误信息:{}", invalidParam.getOutActivityId(), JSONObject.toJSONString(result));
                    }
                } else {
                    log.warn("卡券项目:{}同步失效有赞优惠券活动失败,接口调用失败,response:{}", invalidParam.getOutActivityId(), JSONObject.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("卡券项目:{}同步失效有赞优惠券活动失败,异常信息:{}", invalidParam.getOutActivityId(), e.getMessage());
            }
        } else {
            log.warn("卡券项目:{}同步失效有赞优惠券活动失败,获取clientToken失败!", invalidParam.getOutActivityId());
        }
        return null;
    }

    public Long productGroupCreate(String prjectId, ProductGroupRequest createGroup) {
        log.info("有赞商品分组创建入参: projectId:{} param:{}", prjectId, JSONObject.toJSONString(createGroup));
        String token = getClientToken();
        if (StringUtils.isNotBlank(token)) {
            OkHttpClient client = new OkHttpClient();
            MediaType mediaType = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSONObject.toJSONString(createGroup), mediaType);
            Request request = new Request.Builder()
                    .url(youZanOpenApiProperties.getBaseUrl() + YouZanOpenApiPathTags.YOUZAN_POST_ITEM_GROUP_CREATE)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("ClientToken", token)
                    .build();
            try {
                log.info("卡券项目:{}远程同步创建有赞商品分组开始......", prjectId);
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String respBody = response.body().string();
                    log.info("卡券项目:{}同步创建有赞商品分组接口成功响应,response:{}", prjectId, respBody);
                    YouZanBaseResponse<YouZanItemGroupResponse> result = JSONObject.parseObject(respBody, new TypeReference<YouZanBaseResponse<YouZanItemGroupResponse>>(){});
                    if(result.getSuccess() && result.getData().getId() != null){
                        log.info("卡券项目:{}同步创建有赞商品分组成功,有赞商品分组ID:{}.", prjectId, result.getData().getId());
                        return result.getData().getId();
                    } else {
                        log.warn("卡券项目:{}同步创建有赞商品分组失败,有赞接口返回错误信息:{}", prjectId, JSONObject.toJSONString(result));
                    }
                } else {
                    log.warn("卡券项目:{}同步创建有赞商品分组失败,接口调用失败,response:{}", prjectId, JSONObject.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("卡券项目:{}同步创建有赞商品分组失败,异常信息:{}", prjectId, e.getMessage());
            }
        } else {
            log.warn("卡券项目:{}同步创建有赞商品分组失败,获取clientToken失败!", prjectId);
        }
        return null;
    }

    public String productGroupUpdate(String prjectId, ProductGroupRequest updateGroup) {
        log.info("有赞商品分组修改入参: projectId:{} param::{}", prjectId, JSONObject.toJSONString(updateGroup));
        String token = getClientToken();
        if (StringUtils.isNotBlank(token)) {
            OkHttpClient client = new OkHttpClient();
            MediaType mediaType = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSONObject.toJSONString(updateGroup), mediaType);
            Request request = new Request.Builder()
                    .url(youZanOpenApiProperties.getBaseUrl() + YouZanOpenApiPathTags.YOUZAN_POST_ITEM_GROUP_UPDATE)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("ClientToken", token)
                    .build();
            try {
                log.info("卡券项目:{}远程同步修改有赞商品分组开始......", prjectId);
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String respBody = response.body().string();
                    log.info("卡券项目:{}同步修改有赞商品分组接口成功响应,response:{}", prjectId, respBody);
                    YouZanBaseResponse result = JSONObject.parseObject(respBody, YouZanBaseResponse.class);
                    if(result.getSuccess()){
                        log.info("卡券项目:{}同步修改有赞商品分组成功!", prjectId);
                        return "SUCCESS";
                    } else {
                        log.warn("卡券项目:{}同步修改有赞商品分组失败,有赞接口返回错误信息:{}", prjectId, JSONObject.toJSONString(result));
                    }
                } else {
                    log.warn("卡券项目:{}同步修改有赞商品分组失败,接口调用失败,response:{}", prjectId, JSONObject.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("卡券项目:{}同步修改有赞商品分组失败,异常信息:{}", prjectId, e.getMessage());
            }
        } else {
            log.warn("卡券项目:{}同步修改有赞商品分组失败,获取clientToken失败!", prjectId);
        }
        return null;
    }

    public String productGroupDelete(String prjectId, ProductGroupRequest deleteGroup) {
        log.info("有赞商品分组删除入参: projectId:{} param:{}", prjectId, JSONObject.toJSONString(deleteGroup));
        String token = getClientToken();
        if (StringUtils.isNotBlank(token)) {
            OkHttpClient client = new OkHttpClient();
            MediaType mediaType = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSONObject.toJSONString(deleteGroup), mediaType);
            Request request = new Request.Builder()
                    .url(youZanOpenApiProperties.getBaseUrl() + YouZanOpenApiPathTags.YOUZAN_POST_ITEM_GROUP_DELETE)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("ClientToken", token)
                    .build();
            try {
                log.info("卡券项目:{}远程同步删除有赞商品分组开始......", prjectId);
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String respBody = response.body().string();
                    log.info("卡券项目:{}同步删除有赞商品分组接口成功响应,response:{}", prjectId, respBody);
                    YouZanBaseResponse result = JSONObject.parseObject(respBody, YouZanBaseResponse.class);
                    if(result.getSuccess()){
                        log.info("卡券项目:{}同步删除有赞商品分组成功!", prjectId);
                        return "SUCCESS";
                    } else {
                        log.warn("卡券项目:{}同步删除有赞商品分组失败,有赞接口返回错误信息:{}", prjectId, JSONObject.toJSONString(result));
                    }
                } else {
                    log.warn("卡券项目:{}同步删除有赞商品分组失败,接口调用失败,response:{}", prjectId, JSONObject.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("卡券项目:{}同步删除有赞商品分组失败,异常信息:{}", prjectId, e.getMessage());
            }
        } else {
            log.warn("卡券项目:{}同步删除有赞商品分组失败,获取clientToken失败!", prjectId);
        }
        return null;
    }

    public String productGroupRelation(String prjectId, ProductGroupRelationRequest relationGroup) {
        log.info("有赞商品分组关联商品入参: projectId:{} itemGroupId:{} 商品数量:{}条", prjectId, relationGroup.getItemGroupId(), relationGroup.getItemIds().size());
        String token = getClientToken();
        if (StringUtils.isNotBlank(token)) {
            OkHttpClient client = new OkHttpClient();
            MediaType mediaType = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSONObject.toJSONString(relationGroup), mediaType);
            Request request = new Request.Builder()
                    .url(youZanOpenApiProperties.getBaseUrl() + YouZanOpenApiPathTags.YOUZAN_POST_ITEM_GROUP_RELATION)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("ClientToken", token)
                    .build();
            try {
                log.info("卡券项目:{}远程同步有赞商品分组关联商品信息开始......", prjectId);
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String respBody = response.body().string();
                    log.info("卡券项目:{}同步关联有赞商品分组接口成功响应,response:{}", prjectId, respBody);
                    YouZanBaseResponse result = JSONObject.parseObject(respBody, YouZanBaseResponse.class);
                    if(result.getSuccess()){
                        log.info("卡券项目:{}同步关联有赞商品分组成功!", prjectId);
                        return "SUCCESS";
                    } else {
                        log.warn("卡券项目:{}同步关联有赞商品分组失败,有赞接口返回错误信息:{}", prjectId, JSONObject.toJSONString(result));
                    }
                } else {
                    log.warn("卡券项目:{}同步关联有赞商品分组失败,接口调用失败,response:{}", prjectId, JSONObject.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("卡券项目:{}同步关联有赞商品分组失败,异常信息:{}", prjectId, e.getMessage());
            }
        } else {
            log.warn("卡券项目:{}同步关联有赞商品分组失败,获取clientToken失败!", prjectId);
        }
        return null;
    }

    public String getClientToken() {
        log.info("从resis缓存中获取clientToken参数......");
        RBucket<String> bucket = redissonClient.getBucket(YouZanOpenApiPathTags.YOUZAN_CLIENT_TOKEN);
        if (bucket.isExists() && StringUtils.isNoneBlank(bucket.get())) {
            log.info("从resis缓存中获取到clientToken成功!");
            return bucket.get();
        } else {
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(youZanOpenApiProperties.getBaseUrl() + YouZanOpenApiPathTags.YOUZAN_GET_CLIENT_TOKEN +
                            "?grantType="+YouZanOpenApiPathTags.YOUZAN_GRANT_TYPE+"&clientId=" +
                            youZanOpenApiProperties.getClientId() + "&clientSecret=" + youZanOpenApiProperties.getClientSecret())
                    .get()//默认就是GET请求，可以不写
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .build();
            try {
                log.info("resis缓存中clientToken过期,重新通过接口获取clientToken......");
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String respBody = response.body().string();
                    log.info("有赞获取clientToken接口成功响应,response:{}", respBody);
                    YouZanBaseResponse<YouZanTokenResponse> result = JSONObject.parseObject(respBody, new TypeReference<YouZanBaseResponse<YouZanTokenResponse>>(){});
                    if(result.getSuccess() && StringUtils.isNoneBlank(result.getData().getClientToken())){
                        // log.info("有赞接口获取clientToken成功,返回result:{}", JSONObject.toJSONString(result));
                        String token = result.getData().getClientToken();
                        log.info("将获取的clientToken放入redis缓存,剩余有效时间:{}秒", result.getData().getExpiresIn());
                        if(result.getData().getExpiresIn() > 3){
                            bucket.set(token, result.getData().getExpiresIn() - 3, TimeUnit.SECONDS);
                        }
                        return token;
                    } else {
                        log.warn("有赞接口获取clientToken失败,接口返回结果:{}", JSONObject.toJSONString(result));
                    }
                } else {
                    log.error("通过有赞接口,获取clientToken失败:{}", JSONObject.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("通过有赞接口,获取clientToken异常:{}", e.getMessage());
            }
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
        String baseUrl = "https://r3.isv-dev.youzan.com";
        String clientId = "16777803";
        String clientSecret = "ef4d252a3c58403aac3cce8e67a6cc29";
        /*OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url(baseUrl + YouZanOpenApiPathTags.GET_CLIENT_TOKEN +
                        "?grantType="+YouZanOpenApiPathTags.YOUZAN_GRANT_TYPE+"&clientId=" +
                        clientId + "&clientSecret=" + clientSecret)
                .get()//默认就是GET请求，可以不写
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = client.newCall(request).execute();
        log.info("有赞获取clientToken接口返回结果 response:{}", JSONObject.toJSONString(response));
        YouZanBaseResponse<YouZanTokenResponse> result = JSONObject.parseObject(response.body().string(), new TypeReference<YouZanBaseResponse<YouZanTokenResponse>>() {});
        log.info("有赞获取clientToken返回result:{}", JSONObject.toJSONString(result));
        if(result.getSuccess() && StringUtils.isNoneBlank(result.getData().getClientToken())){
            String token = result.getData().getClientToken();
            log.info("有赞获取clientToken成功,将token放入redis缓存,剩余有效时间:{}秒", result.getData().getExpiresIn());
            if(result.getData().getExpiresIn() > 3){
                //bucket.set(token, result.getData().getExpiresIn() - 3, TimeUnit.SECONDS);
            }
        } else {
            log.warn("有赞获取clientToken失败!");
        }*/

        String bodyStr = "{\"code\":200,\"message\":\"成功\",\"success\":true,\"timestamp\":1732872332880,\"data\":17327839275}";
        YouZanBaseResponse<Long> result = JSONObject.parseObject(bodyStr, new TypeReference<YouZanBaseResponse<Long>>() {});
        log.info("有赞获取clientToken返回result:{}", JSONObject.toJSONString(result));
        if(result.getSuccess()){
            Long activityId = result.getData();
            System.out.println(activityId);
        } else {
            log.warn("有赞获取clientToken失败!");
        }
    }
}
