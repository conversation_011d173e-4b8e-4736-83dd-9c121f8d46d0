package com.shuyun.fast.youzan.coupon.request;

import lombok.Data;

import java.util.List;

@Data
public class CardUsingRule {

    // 适⽤的店铺范围类型  2:部分店铺可⽤  3:全部店铺可⽤
    private Integer applicableShopRangeType;
    // 适⽤的店铺ids，需有赞店铺ID，部分店铺可⽤必填
    private List<String> applicableShopIds;
    // 适⽤的⽹店商品范围类型 1:不限制  2:部分可⽤  3:部分不可⽤, 创建兑换券时必须设置为1
    private Integer applicableOnlineGoodsRangeType;
    // 适⽤的⽹店商品ID列表，部分商品可⽤/不可⽤时必填，与applicableOfflineGoodsGroupIds ⼆选⼀
    private List<Long> applicableOnlineGoodsIds;
    // 适⽤的⽹店商品分组ID列表，部分商品可⽤/不可⽤时必填，与 applicableOnlineGoodsIds ⼆选⼀
    private List<Long> applicableOfflineGoodsGroupIds;
    // 使⽤⻔槛类型  0:⽆⻔槛;  1:满元
    private Integer thresholdType;
    // 使⽤⻔槛 0：⽆⻔槛（默认值） n：⻔槛值（单位：分）,thresholdType=1时必填
    private Long thresholdAmount;
    // 最⼤折扣上限，单位是分  0：代表没有折扣上限(默认值)
    private Long maxDiscountAmount;
    // 是否禁⽌叠加优惠
    private Boolean isForbidOverlayPreferential;
    // 优惠券使⽤⻔槛折扣，例如：8折传80；下单时会计算商品售价与划线价的折扣⽐例，当⽐例⼩于该值时不可下单；未填写时不做限制
    private Long discountLimit;
}
