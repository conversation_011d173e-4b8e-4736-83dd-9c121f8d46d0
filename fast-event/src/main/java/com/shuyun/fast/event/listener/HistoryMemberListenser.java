package com.shuyun.fast.event.listener;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_0.EbrandMemberSyncService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import io.micronaut.context.annotation.Property;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.Map;

/**
 * @Author:luoruizhe
 * @Date:2024/12/26 13:47
 */
@Singleton
@Slf4j
public class HistoryMemberListenser {

    private final MemberService memberService;

    private final EbrandMemberSyncService ebrandMemberSyncService;
    private Object ModelTags;

    public HistoryMemberListenser(MemberService memberService, EbrandMemberSyncService ebrandMemberSyncService) {
        this.memberService = memberService;
        this.ebrandMemberSyncService = ebrandMemberSyncService;
    }

    @KafkaListener(groupId = "fast-member-wechat", offsetReset = OffsetReset.LATEST, threads = 2, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = "fast.created.member.wechat")
    public void HistoryMemberCreate(Map<String,Object> param) {
        log.info("created历史会员创建 topic:{} ", param);
        //调用会员创建接口创建会员  /member/create
        JSONObject event = JSONUtil.parseObj(param);
        memberService.createHisMember(event);
    }

    @KafkaListener(groupId = "fast-member-channel", offsetReset = OffsetReset.LATEST, threads = 2, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = "fast.register.history.member.first")
    public void HistoryMemberRegister(Map<String,Object> param) {
        log.info("register历史会员注册 topic:{} value:{}", param);
        //调用会员注册接口
        JSONObject event = JSONUtil.parseObj(param);
        memberService.registerHisMember(event);
    }

    @KafkaListener(groupId = "fast-member-channel-unbind",  offsetReset = OffsetReset.LATEST, threads = 24, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = "fast.unbind.member.channel")
    public void YingJiaHistoryMemberUnbind(Map<String,Object> param) {
        log.info("unbindMessage    value:{}",param );
        //调用会员渠道解绑接口解绑会员  /member/channel/unbind
        JSONObject event = JSONUtil.parseObj(param);
        memberService.unbindHisMember(event);
    }

    @KafkaListener(groupId = "fast-member-channel-benefit",  offsetReset = OffsetReset.LATEST, threads = 24, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = "fast.benefit.member.channel")
    public void importBatch(Map<String,Object> param) {
        log.info("EndPointSend topic:{}    value:{}", param);
        //调用卡券发放接口发放卡券
        JSONObject event = JSONUtil.parseObj(param);
        memberService.importBatch(event);
    }

    @KafkaListener(groupId = "fast-ebrandMember-taobao-20250225", offsetReset = OffsetReset.LATEST, threads =24 , properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = "fast.ebrand.member.taobao_20250225")
    public void taobaoEbrandSync(String event) throws Exception {
        log.info("taobaoEbrandSync event:{}", event);
        //OrderOuidParam request = JsonUtil.outPutDeserialize(event, OrderOuidParam.class);
        ebrandMemberSyncService.taobaoEbrandSync(event);
    }

    @KafkaListener(groupId = "fast-ebrandMembfast-ebrandMember-taobaoer-douyin", offsetReset = OffsetReset.LATEST, threads = 24, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = "fast.ebrand.member.douyin")
    public void douyinEbrandSync(String event) throws Exception {
        //log.info("douyinEbrandSync event:{}", event);
        //  OrderOuidParam request = JsonUtil.outPutDeserialize(event, OrderOuidParam.class);
        ebrandMemberSyncService.douyinEbrandSync(event);
    }

}
