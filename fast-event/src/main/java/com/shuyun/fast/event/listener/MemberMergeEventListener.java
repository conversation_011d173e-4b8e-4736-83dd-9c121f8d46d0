package com.shuyun.fast.event.listener;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_0.GradeService;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.v1_0_0.param.grade.GradeMergeParam;
import com.shuyun.fast.youzan.coupon.event.MemberMergeEvent;
import com.shuyun.fast.youzan.coupon.event.MemberMergeId;
import com.shuyun.pip.util.UUIDUtils;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@Singleton
public class MemberMergeEventListener {

    private final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    @Inject
    private GradeService gradeService;

    @KafkaListener(groupId = "cbanner-member-merge-event", offsetReset = OffsetReset.LATEST)
    @Topic(value = ModelTags.EVENT_TOPIC_MEMBER_MERGE)
    public void memberMergeEvent(MemberMergeEvent event){
        log.info("memberMergeEvent开始=====> event:{}", JSONObject.toJSONString(event));
        long startTime = System.currentTimeMillis();
        // 会员归属合并
        mergeMemberRule(event.getMainMemberId(), event.getMergeMemberId());
        // 会员公众号粉丝合并
        mergeMemberFans(event.getMainMemberId(), event.getMergeMemberId());

        log.info("开始主卡会员mainMemberId:{}订单信息合并......", event.getMainMemberId());
        // 全渠道主订单合并
        mergeMemberOrder(event.getMainMemberId(), event.getMergeMemberId());
        // 全渠道子订单合并
        mergeMemberOrderItem(event.getMainMemberId(), event.getMergeMemberId());
        // 全渠道主退单合并
        mergeMemberRefund(event.getMainMemberId(), event.getMergeMemberId());
        // 全渠道子退单合并
        mergeMemberRefundItem(event.getMainMemberId(), event.getMergeMemberId());
        // 总主订单合并
        mergeMainOrder(event.getMainMemberId(), event.getMergeMemberId());
        // 总子订单合并
        mergeMainOrderItem(event.getMainMemberId(), event.getMergeMemberId());
        log.info("主卡会员mainMemberId:{}订单信息合并结束", event.getMainMemberId());

        // 客户订单和退单合并
        log.info("开始主卡会员mainMemberId:{}客户订单信息合并......", event.getMainMemberId());
        // 客户主订单合并
        mergeConsumerOrder(event.getMainMemberId(), event.getMergeMemberId());
        // 客户子订单合并
        mergeConsumerOrderItem(event.getMainMemberId(), event.getMergeMemberId());
        // 客户主退单合并
        mergeConsumerRefund(event.getMainMemberId(), event.getMergeMemberId());
        // 客户子退单合并
        mergeConsumerRefundItem(event.getMainMemberId(), event.getMergeMemberId());
        log.info("主卡会员mainMemberId:{}客户订单信息合并结束", event.getMainMemberId());

        // 会员等级合并
        mergeMemberGrade(event.getBizCode(), event.getMainMemberId(), event.getMergeMemberId());

        // 记录合并结果
        Map<String, Object> resultMap = new HashMap<>(4);
        resultMap.put("id", event.getId());
        resultMap.put("mainMemberId", event.getMainMemberId());
        resultMap.put("mergeMemberId", event.getMergeMemberId());
        resultMap.put("isSuccess", "Y");
        resultMap.put("result", "合卡成功");
        dataapiHttpSdk.upsert(ModelTags.DATA_FQN_MEMBER_MERGE, event.getId(), resultMap, false);
        log.info("memberMergeEvent结束=====> 耗时:{}毫秒", System.currentTimeMillis() - startTime);
    }

    private void mergeMemberRule(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}归属信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        updateMap.put("member", Collections.singletonMap("id", mainMemberId));
        String querySql = "SELECT id FROM " + ModelTags.DATA_FQN_MEMBER_RULE + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> memberRuleResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (memberRuleResult.getIsSuccess()) {
            if(!memberRuleResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(memberRuleResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的归属信息共:{}条,开始合并归属信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    dataapiHttpSdk.update(ModelTags.DATA_FQN_MEMBER_RULE, memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的归属信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的归属信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}归属信息合并结束", mainMemberId);
    }

    private void mergeMemberFans(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}微信粉丝信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        updateMap.put("member", Collections.singletonMap("id", mainMemberId));
        String querySql = "SELECT id FROM " + ModelTags.DATA_FQN_WECHAT_FANS_RELATION + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> memberFansResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (memberFansResult.getIsSuccess()) {
            if(!memberFansResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(memberFansResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的微信粉丝信息共:{}条,开始合并微信粉丝信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    dataapiHttpSdk.update(ModelTags.DATA_FQN_WECHAT_FANS_RELATION, memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的微信粉丝信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的微信粉丝信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}微信粉丝信息合并结束", mainMemberId);
    }

    private void mergeMemberOrder(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}全渠道主订单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        updateMap.put("member", Collections.singletonMap("id", mainMemberId));
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_MEMBER_ORDER, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> memberOrderResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (memberOrderResult.getIsSuccess()) {
            if(!memberOrderResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(memberOrderResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道主订单信息共:{}条,开始合并全渠道主订单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据主订单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_MEMBER_ORDER, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道主订单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道主订单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}全渠道主订单信息合并结束", mainMemberId);
    }

    private void mergeMemberOrderItem(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}全渠道子订单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        updateMap.put("member", Collections.singletonMap("id", mainMemberId));
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_MEMBER_ORDER_ITEM, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> memberOrderItemResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (memberOrderItemResult.getIsSuccess()) {
            if(!memberOrderItemResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(memberOrderItemResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道子订单信息共:{}条,开始合并全渠道子订单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据子订单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_MEMBER_ORDER_ITEM, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道子订单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道子订单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}全渠道子订单信息合并结束", mainMemberId);
    }

    private void mergeMemberRefund(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}全渠道主退单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        updateMap.put("member", Collections.singletonMap("id", mainMemberId));
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_MEMBER_REFUND, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> memberRefundResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (memberRefundResult.getIsSuccess()) {
            if(!memberRefundResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(memberRefundResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道主退单信息共:{}条,开始合并全渠道主退单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据主退单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_MEMBER_REFUND, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道主退单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道主退单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}全渠道主退单信息合并结束", mainMemberId);
    }

    private void mergeMemberRefundItem(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}全渠道子退单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        updateMap.put("member", Collections.singletonMap("id", mainMemberId));
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_MEMBER_REFUND_ITEM, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> memberRefundItemResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (memberRefundItemResult.getIsSuccess()) {
            if(!memberRefundItemResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(memberRefundItemResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道子退单信息共:{}条,开始合并全渠道子退单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据子退单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_MEMBER_REFUND_ITEM, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道子退单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的全渠道子退单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}全渠道子退单信息合并结束", mainMemberId);
    }

    private void mergeMainOrder(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}总主订单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        updateMap.put("member", Collections.singletonMap("id", mainMemberId));
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_MAIN_ORDER, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> mainOrderResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (mainOrderResult.getIsSuccess()) {
            if(!mainOrderResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(mainOrderResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的总主订单信息共:{}条,开始合并总主订单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据总主订单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_MAIN_ORDER, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的总主订单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的总主订单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}总主订单信息合并结束", mainMemberId);
    }

    private void mergeMainOrderItem(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}总子订单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_MAIN_ORDER_ITEM, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> mainOrderItemResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (mainOrderItemResult.getIsSuccess()) {
            if(!mainOrderItemResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(mainOrderItemResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的总子订单信息共:{}条,开始合并总子订单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据总子订单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_MAIN_ORDER_ITEM, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的总子订单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的总子订单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}总子订单信息合并结束", mainMemberId);
    }

    private void mergeConsumerOrder(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}客户主订单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_CONSUMER_ORDER, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> consumerOrderResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (consumerOrderResult.getIsSuccess()) {
            if(!consumerOrderResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(consumerOrderResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户主订单信息共:{}条,开始合并客户主订单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据总子订单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_CONSUMER_ORDER, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户主订单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户主订单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}客户主订单信息合并结束", mainMemberId);
    }

    private void mergeConsumerOrderItem(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}客户子订单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_CONSUMER_ORDER_ITEM, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> consumerOrderItemResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (consumerOrderItemResult.getIsSuccess()) {
            if(!consumerOrderItemResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(consumerOrderItemResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户子订单信息共:{}条,开始合并客户子订单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据总子订单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_CONSUMER_ORDER_ITEM, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户子订单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户子订单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}客户子订单信息合并结束", mainMemberId);
    }

    private void mergeConsumerRefund(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}客户主退单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_CONSUMER_REFUND, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> consumerRefundResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (consumerRefundResult.getIsSuccess()) {
            if(!consumerRefundResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(consumerRefundResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户主退单信息共:{}条,开始合并客户主退单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据总子订单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_CONSUMER_REFUND, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户主退单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户主退单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}客户主退单信息合并结束", mainMemberId);
    }

    private void mergeConsumerRefundItem(String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}客户子退单信息合并......", mainMemberId);
        Map<String, Object> queryMap = new HashMap<>(2);
        queryMap.put("memberId", mergeMemberId);
        Map<String, Object> updateMap = new HashMap<>(2);
        updateMap.put("memberId", mainMemberId);
        String querySql = "SELECT id FROM " + String.format(ModelTags.DATA_FQN_TRADE_CONSUMER_REFUND_ITEM, "CBANNER") + " WHERE memberId = :memberId";
        BaseResponse<MemberMergeId> consumerRefundItemResult = dataapiHttpSdk.execute(querySql, queryMap);
        if (consumerRefundItemResult.getIsSuccess()) {
            if(!consumerRefundItemResult.getData().isEmpty()){
                List<MemberMergeId> idList = JSONArray.parseArray(JSONObject.toJSONString(consumerRefundItemResult.getData()), MemberMergeId.class);
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户子退单信息共:{}条,开始合并客户子退单信息到主卡......", mainMemberId, mergeMemberId, idList.size());
                for (MemberMergeId memberMergeId : idList) {
                    // 根据总子订单号更新memberId信息
                    dataapiHttpSdk.update(String.format(ModelTags.DATA_FQN_TRADE_CONSUMER_REFUND_ITEM, "CBANNER"), memberMergeId.getId(), updateMap, false);
                }
            } else {
                log.info("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户子退单信息查询为空,无需合并!", mainMemberId, mergeMemberId);
            }
        } else {
            log.warn("主卡会员mainMemberId:{}对应副卡会员mergeMember:{}的客户子退单信息查询失败!", mainMemberId, mergeMemberId);
        }
        log.info("主卡会员mainMemberId:{}客户子退单信息合并结束", mainMemberId);
    }

    private void mergeMemberGrade(String bizCode, String mainMemberId, String mergeMemberId){
        log.info("开始主卡会员mainMemberId:{}等级合并......", mainMemberId);
        GradeMergeParam param = new GradeMergeParam();
        param.setBizCode(bizCode);
        param.setGradeBizType("GRADE");
        param.setMainMemberId(mainMemberId);
        param.setMergeMemberIdList(Arrays.asList(mergeMemberId));
        param.setTraceId(UUIDUtils.generatorUuidWithOutSplit());
        param.setDescription("等级定制合并");
        param.setGradeMergeType("RECALCULATE");
        param.setUseHighestEffectTimeType("RESET");
        log.info("主卡会员mainMemberId:{}等级合并入参:{}", mainMemberId, JSONObject.toJSONString(param));
        gradeService.mergeGrade(param);
        log.info("主卡会员mainMemberId:{}等级合并结束", mainMemberId);
    }
}
