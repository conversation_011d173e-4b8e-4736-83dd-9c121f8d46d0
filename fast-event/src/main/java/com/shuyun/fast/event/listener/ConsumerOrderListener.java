package com.shuyun.fast.event.listener;

import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_1.TradeService;
import com.shuyun.fast.v1_0_1.param.trade.TradeOrderSyncParam;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class ConsumerOrderListener {

    @Inject
    private TradeService tradeService;
    @KafkaListener(groupId = "consumer-order", offsetReset = OffsetReset.LATEST, threads = 8)
    @Topic(value = ModelTags.EVENT_TOPIC_ORDER)
    public void onEvent(TradeOrderSyncParam event){
        tradeService.consumerOrder(event);
    }
}
