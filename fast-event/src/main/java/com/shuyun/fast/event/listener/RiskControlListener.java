package com.shuyun.fast.event.listener;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_0.RiskControlService;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Singleton
@Slf4j
public class RiskControlListener {

    @Inject
    private RiskControlService riskControlService;

    @KafkaListener(groupId = "risk-control-checklist", offsetReset = OffsetReset.LATEST)
    @Topic(value = ModelTags.EVENT_TOPIC_RISK_CONTROL_CHECKLIST)
    public void riskCheckList(Map<String, Object> param) {
        JSONObject event = (JSONObject) JSONObject.toJSON(param);
        log.info("check list event:{}", event);
        try {
            riskControlService.checkList(event);
        } catch (Exception e) {
            log.error("同步黑名单异常, event:{}", event, e);
        }
    }
}
