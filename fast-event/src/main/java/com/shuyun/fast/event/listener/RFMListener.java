package com.shuyun.fast.event.listener;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_0.RFMService;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import io.micronaut.context.annotation.Property;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Singleton
@Slf4j
public class RFMListener {

    @Inject
    private RFMService rfmService;

    @KafkaListener(groupId = "rfm-calculate",  offsetReset = OffsetReset.LATEST, threads = 24, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = ModelTags.EVENT_TOPIC_RFM_CALCULATE)
    public void calculate(Map<String, Object> param) {
        JSONObject event = (JSONObject) JSONObject.toJSON(param);
        log.info("rfm calculate event:{}", event);
        try {
            rfmService.calculate(event);
        } catch (Exception e) {
            log.error("RFM计算异常, event:{}", event, e);
        }
    }
}
