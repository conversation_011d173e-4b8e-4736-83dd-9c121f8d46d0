package com.shuyun.fast.event.listener;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_0.DepartmentService;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Singleton
@Slf4j
public class DepartmentSyncListener {

    @Inject
    private DepartmentService departmentService;

    @KafkaListener(groupId = "consumer-department-sync", offsetReset = OffsetReset.LATEST, threads = 1)
    @Topic(value = ModelTags.EVENT_TOPIC_DEPARTMENT_SYNC_ORG)
    public void syncOrg(Map<String, Object> param) {
        JSONObject event = (JSONObject) JSONObject.toJSON(param);
        log.info("sync org event:{}", event);
        try {
            departmentService.syncOrg(event);
        } catch (Exception e) {
            log.error("部门同步组织异常, event:{}", event, e);
        }
    }
}
