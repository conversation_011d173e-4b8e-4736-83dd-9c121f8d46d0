package com.shuyun.fast.event.listener;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_0.BojunInterfaceService;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * @title BojunSyncListener
 * @description 伯俊数据同步
 * @create 2024/12/6 9:29
 */
@Singleton
@Slf4j
public class BojunSyncListener {

    @Inject
    private BojunInterfaceService bojunInterfaceService;

    @KafkaListener(groupId = "consumer-bojun-point-sync", offsetReset = OffsetReset.LATEST, threads = 1)
    @Topic(value = ModelTags.EVENT_TOPIC_BOJUN_POINT)
    public void pointSync(Map<String,Object> param){
        JSONObject event = (JSONObject) JSONObject.toJSON(param);
        log.info("fast.event.bojun.point.sync:{}",event);
        try {
            JSONObject requestParam = new JSONObject(){{
                JSONArray data = new JSONArray(){{
                    JSONObject dataObj = new JSONObject(){{
                        put("type",event.getInteger("type"));
                        put("bill_no",event.getString("bill_no"));
                        put("vip_score",event.getInteger("vip_score"));
                    }};
                    add(dataObj);
                }};
                put("data",data);
            }};
            bojunInterfaceService.doPost(requestParam);
        }catch (Exception e){
            log.error("积分明细同步伯俊出现异常：",e);
        }

    }
}
