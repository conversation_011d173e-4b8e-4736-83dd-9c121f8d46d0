package com.shuyun.fast.event.listener;

import com.shuyun.fast.kafka.KafkaNewTopics;
import com.shuyun.fast.kafka.KafkaSender;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import io.micronaut.core.annotation.Order;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.NewTopic;

@Singleton
@Slf4j
public class HelloEventListener {

    @Inject
    private KafkaSender sender;

    @Inject
    private KafkaNewTopics topics;//控制DI顺序,勿动
    /**
     * kafka多分区消息监听demo
     */
    @PostConstruct
    private void init(){
        for(int i=0; i<24; i++){
            String key = String.valueOf(i);
            sender.send("fast.event.hello2006", key, "hello partition" + key);
        }
    }

    @KafkaListener(groupId = "fast-event", offsetReset = OffsetReset.EARLIEST, threads = 4)
    @Topic(value = "fast.event.hello2006")
    public void onHelloEvent(String event) {
        log.info("received hello event:{}", event);
    }
}
