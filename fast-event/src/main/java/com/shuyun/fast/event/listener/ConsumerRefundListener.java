package com.shuyun.fast.event.listener;

import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_1.TradeService;
import com.shuyun.fast.v1_0_1.param.trade.TradeRefundSyncParam;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Singleton
@Slf4j
public class ConsumerRefundListener {

    @Inject
    private TradeService tradeService;

    @KafkaListener(groupId = "consumer-refund", offsetReset = OffsetReset.LATEST, threads = 8)
    @Topic(value = ModelTags.EVENT_TOPIC_REFUND)
    public void onEvent(TradeRefundSyncParam event){
        tradeService.consumerRefund(event);
    }
}
