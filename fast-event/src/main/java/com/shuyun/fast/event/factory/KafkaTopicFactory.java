package com.shuyun.fast.event.factory;

import com.shuyun.fast.base.ModelTags;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Requires;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.CreateTopicsOptions;
import org.apache.kafka.clients.admin.NewTopic;


@Requires(bean = AdminClient.class)
@Factory
public class KafkaTopicFactory {

    @Bean
    CreateTopicsOptions options() {
        return new CreateTopicsOptions().timeoutMs(5000).retryOnQuotaViolation(false);
    }

    /**
     * 创建kafka多分区topic
     * @return
     */
    @Bean
    NewTopic helloTopic() {
        return new NewTopic("fast.event.hello2006", 24, (short) 1);
    }

    @Bean
    NewTopic fastOrderTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_ORDER, 24, (short) 1);
    }

    @Bean
    NewTopic fastRefundTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_REFUND, 24, (short) 1);
    }

    @Bean
    NewTopic pointSyncTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_BOJUN_POINT, 24, (short) 1);
    }

    @Bean
    NewTopic syncOrgTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_DEPARTMENT_SYNC_ORG, 24, (short) 1);
    }

    @Bean
    NewTopic riskCheckListTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_DEPARTMENT_SYNC_ORG, 24, (short) 1);
    }

    @Bean
    NewTopic fastProjectInfoCreateTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_PROJECT_CREATE, 24, (short) 1);
    }

    @Bean
    NewTopic fastProjectInfoUpdateTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_PROJECT_UPDATE, 24, (short) 1);
    }

    @Bean
    NewTopic fastProjectInfoOfflineTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_PROJECT_OFFLINE, 24, (short) 1);
    }

    @Bean
    NewTopic fastMemberMergeTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_MEMBER_MERGE, 24, (short) 1);
    }

    @Bean
    NewTopic hisMemberDealTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_HISMEMBER_DEAL, 24, (short) 8);
    }

    @Bean
    NewTopic hiswechatMemberDealTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_WECHAT_HISMEMBER_DEAL, 24, (short) 8);
    }

    @Bean
    NewTopic hisTaobaoMemberDealTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_TAOBAO_HISMEMBER_DEAL, 24, (short) 8);
    }

    @Bean
    NewTopic hisGradeDealTopic() {
        return new NewTopic(ModelTags.EVENT_TOPICHISGRADE_DEAL, 24, (short) 8);
    }

    @Bean
    NewTopic hisPointDealTopic() {
        return new NewTopic(ModelTags.EVENT_TOPICHISPOINT_DEAL, 24, (short) 8);
    }

    @Bean
    NewTopic ouidDealTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_HISMEMBER_OUID_DEAL, 24, (short) 8);
    }

    @Bean
    NewTopic hisMemberGuidePushTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_HISMEMBER_GUIDE_PUSH, 24, (short) 8);
    }

    @Bean
    NewTopic memberOrderToGuideTopic() {
        return new NewTopic(ModelTags.EVENT_TOPIC_MEMBER_ORDER_TO_GUIDE, 24, (short) 8);
    }
}
