package com.shuyun.fast.v1_0_0.param.grade;

import lombok.Data;

import java.util.List;

@Data
public class GradeMergeParam {

    private String bizCode;
    private String gradeBizType;
    private String channelType;
    private String mainMemberId;
    private List<String> mergeMemberIdList;
    private String traceId;
    private String description;
    // 等级合并规则类型   HIGHEST:取主副中最高等级;   RECALCULATE:基于等级重算规则重算
    private String gradeMergeType;
    // 取高后等级生效时间  RESET:取系统当前时间;   UNCHANGE:取主卡有效期  默认RESET
    private String useHighestEffectTimeType;
}
