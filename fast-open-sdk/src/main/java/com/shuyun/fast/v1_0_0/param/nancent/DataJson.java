package com.shuyun.fast.v1_0_0.param.nancent;

import io.micronaut.core.annotation.Introspected;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @title DataJson
 * @description 南讯奇门数据对象
 * @create 2024/12/6 13:41
 */
@Data
@Introspected
public class DataJson {
    @NotEmpty
    private String dataType;
    @NotEmpty
    private String distributionId;
    @NotEmpty
    private String message;
    @NotEmpty
    private String sign;
}
