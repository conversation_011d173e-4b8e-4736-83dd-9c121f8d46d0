package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponListParam extends ApiBaseParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "项目ID")
    private String projectId;
    @Schema(title = "卡劵实例状态,不传入默认查询所有状态下的实例,CREATED:已发放,ACTIVATED:已启用,DISCARDED:已作废,EFFECTED:已生效,EXPIRED:已失效,LOCKED:已锁定,USED:已使用")
    private String state;
    @Schema(title = "当前页:从0开始", requiredMode = Schema.RequiredMode.REQUIRED, example = "0", defaultValue = "0")
    private Integer page = 0;
    @Schema(title = "页大小:不超过20", requiredMode = Schema.RequiredMode.REQUIRED, example = "20", defaultValue = "20")
    private Integer pageSize = 20;
    @Schema(title = "查询条件参数", description = "查询条件参数是动态传入,例子:{\"title\":{\"EQ\":\"openapi测试项目\"},\"status\":{\"EQ\":\"RELEASED\"},\"branch\":{\"IN\":[\"2e3cc427\"]},\"updateAt\":{\"BETWEEN\":[\"2022-10-21 14:24:28\",\"2022-10-21 14:24:30\"]}}，可通过响应结果值中的属性名称过滤")
    private String queryParams;
    @Schema(title = "是否展示项目信息", description = "为false的话响应参数中没有project")
    private Boolean showProject = false;
    @Schema(title = "是否填充选择器数据", defaultValue = "false")
    private Boolean isSelector = false;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_LIST;
    }
}
