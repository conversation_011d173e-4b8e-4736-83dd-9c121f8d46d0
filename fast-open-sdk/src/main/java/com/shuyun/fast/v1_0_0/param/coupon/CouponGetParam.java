package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponGetParam extends ApiBaseParam {

    @Schema(title = "项目id", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32)
    //@NotEmpty
    private String projectId;
    @Schema(title = "券码", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128)
    @NotEmpty
    private String code;
    @Schema(title = "是否展示项目信息", description = "为false的话响应参数中没有project")
    private Boolean showProject = false;
    @Schema(title = "是否填充选择器数据", defaultValue = "false")
    private Boolean isSelector = false;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_GET;
    }
}
