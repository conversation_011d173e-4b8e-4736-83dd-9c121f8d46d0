package com.shuyun.fast.v1_0_0.param.nancent;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @title Extend
 * @description 南讯会员扩展属性
 * @create 2024/11/25 16:12
 */
@Data
@Introspected
public class Extend {
    @Schema(title = "宝宝的出生年月日",example = "2022-01-01")
    private String babyBirthday;
    @Schema(title = "出生年月日",example = "2022-01-01")
    private String birthday;
    @Schema(title = "会员所在城市",example = "厦门")
    private String city;
    @Schema(title = "会员所在省份",example = "福建")
    private String province;
    @Schema(title = "会员的邮箱",example = "<EMAIL>")
    private String email;
    @Schema(title = "性别：1（男性）2（女性）",example = "1")
    private String sex;
    @Schema(title = "会员姓名",example = "张三")
    private String name;

}
