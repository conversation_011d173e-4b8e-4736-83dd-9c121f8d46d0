package com.shuyun.fast.v1_0_0.param.mdm;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@JsonFilter("youzanProductSyncParamFilter")
public class YouzanProductSyncParam extends ApiBaseParam implements ModelTag {
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title="渠道类型", example = "YOUZAN")
    private String channelType;
    @NotEmpty
    @Schema(title="有赞商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3361256")
    private Long productId;
    @Schema(title="商品名称", maxLength = 255, example = "外套")
    private String productName;
    @Schema(title="商品描述", example = "性价比高 超值")
    private String productDesc;
    @Schema(title="商品批次编码", example = "10001")
    private String sqId;
    @Schema(title="吊牌价,单位:元", example = "20")
    private Double tagPrice;
    @Schema(title="零售价,单位:元", example = "20")
    private Double retailPrice;
    @Schema(title="类型code", maxLength = 64, example = "001")
    private String deptCode;
    @Schema(title="类型名称", maxLength = 128, example = "男装")
    private String deptName;
    @Schema(title="商品图片URL", example = "[\"http://www.xx.com/url?id=13256\"]")
    private List<String> picture;
    @Schema(title="商品规格编号", example = "[\"Z25010472Q1536\",\"Z25010472Q1537\"]")
    private List<String> productCodeList;
    @Schema(title = "8时区创建时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @Schema(title = "8时区更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @Schema(title="是否有效(Y-有效; N-失效)", maxLength = 4, example = "Y")
    private String isValid = "Y";
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();

    @Override
    public String apiName() {
        return ApiTags.API_NAME_YOUZAN_PRODUCT_SYNC;
    }

    @Override
    public String fqn() {
        return ModelTags.DATA_FQN_YOUZAN_PRODUCT;
    }
}
