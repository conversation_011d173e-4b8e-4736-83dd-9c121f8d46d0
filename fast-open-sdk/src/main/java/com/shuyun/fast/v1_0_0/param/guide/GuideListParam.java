package com.shuyun.fast.v1_0_0.param.guide;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class GuideListParam extends ApiBaseParam {
    @Schema(title = "门店编码", required = true)
    @NotBlank
    private String shopCode;
    @Schema(title = "在职状态 1=已激活，2=已禁用，4=未激活，5=退出企业")
    private String guideStatus;
    @Schema(title = "书类")
    private List<String> bookCategories;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_GUIDE_LIST;
    }
}
