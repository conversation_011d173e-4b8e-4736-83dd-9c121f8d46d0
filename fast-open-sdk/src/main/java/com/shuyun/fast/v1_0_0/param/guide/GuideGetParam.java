package com.shuyun.fast.v1_0_0.param.guide;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class GuideGetParam extends ApiBaseParam {
    @Schema(title = "导购工号", required = true)
    @NotBlank
    private String guideId;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_GUIDE_GET;
    }
}
