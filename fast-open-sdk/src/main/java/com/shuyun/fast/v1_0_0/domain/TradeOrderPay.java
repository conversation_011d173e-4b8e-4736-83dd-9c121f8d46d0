package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.v1_0_0.constant.OrderOwnerType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TradeOrderPay implements Serializable, ModelTag {
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title = "订单归属类型 consumer-消费者  member-会员")
    @JsonIgnore
    private String orderOwnerType;
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(hidden = true, title = "渠道", example = "TAOBAO")
    private String channelType;
    @Schema(title = "支付方式编码", example = "1")
    private String payWayCode;
    @Schema(title = "支付方式名称", example = "现金")
    private String payWayName;
    @Schema(title = "支付金额", example = "1.00")
    private Double payment;
    @Schema(hidden = true, title = "货币", example = "人民币")
    private String currency;
    @Schema(hidden = true, title = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128, example = "087989aaf6af4e58897794d18axxxxx")
    private String orderId;
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();

    @Override
    public String fqn() {
        if(orderOwnerType.equals(OrderOwnerType.MEMBER)){
            return ModelTags.DATA_FQN_TRADE_MEMBER_ORDER_PAY;
        }
        return ModelTags.DATA_FQN_TRADE_CONSUMER_ORDER_PAY;
    }
}
