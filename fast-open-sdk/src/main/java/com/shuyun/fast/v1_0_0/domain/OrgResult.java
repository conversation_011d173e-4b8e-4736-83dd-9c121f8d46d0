package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgResult implements Serializable {

    private Long id;

    private  String name;

    private  String code;

    private Long type;

    private String typeName;

    private String typeCode;

    private Long parentId;


    private String parentName;

    private String path;
    private Integer province;
    private Integer city;
    private Integer county;
    private  String address;
    private String tenantId;
    private Long creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime created;
    private Integer orgCount;
    private Integer userCount;
    private Boolean enabled;
    private Long updator;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updated;

    private String pathName;
    private String businessScopeId;
    private String businessScopeName;
    private String systemCode;
}
