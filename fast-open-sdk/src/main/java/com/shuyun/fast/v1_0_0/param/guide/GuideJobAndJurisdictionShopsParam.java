package com.shuyun.fast.v1_0_0.param.guide;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @title Guide
 * @description updateGuideJobAndJurisdictionShopsParam
 * @create 2025/8/26 18:27
 */
@Data
public class GuideJobAndJurisdictionShopsParam {

    @Schema(title = "区域")
    private List<String> areaCodes;

    @Schema(title = "工号")
    private String employeeId;

    @Schema(title = "职位名称")
    private String jobName;

    @Schema(title = "门店")
    private List<String> shopCodes;
}
