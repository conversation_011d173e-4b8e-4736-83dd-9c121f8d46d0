package com.shuyun.fast.v1_0_0.param;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Introspected
public class PointRecordParam {

    @Schema(title = "会员ID")
    @NotNull
    private String memberId;
    @Schema(title = "动作:SEND/DEDUCT")
    @NotNull
    private String action;
    @Schema(title = "渠道类型")
    @NotNull
    private String channelType;
    @Schema(title = "积分值")
    @NotNull
    private Integer point;
    @Schema(title = "生效时间")
    private String effectiveDate;
    @Schema(title = "过期时间")
    private String overdueDate;
    @Schema(title = "创建时间")
    @NotNull
    private String createdDate;
    @Schema(title = "key")
    @NotNull
    private String key;
    @Schema(title = "备注")
    private String description;

}
