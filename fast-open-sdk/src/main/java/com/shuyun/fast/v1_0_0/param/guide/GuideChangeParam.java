package com.shuyun.fast.v1_0_0.param.guide;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class GuideChangeParam {
    @Schema(title = "会员id")
    private String memberId;
    @Schema(title = "原跟进导购工号")
    private String handoverEmployeeId;
    @Schema(title = "接替导购工号")
    private String takeoverEmployeeId;
    @Schema(title = "unionId")
    private String unionId;

}
