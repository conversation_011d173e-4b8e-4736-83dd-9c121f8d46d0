package com.shuyun.fast.v1_0_0.param.point;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class PointModifyParam extends ApiBaseParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "积分业务类型,不传,默认为: POINT", defaultValue = "POINT")
    private String pointBizType = "POINT";
    @Schema(title = "备注", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255, example = "备注")
    @NotEmpty
    private String description;
    @Schema(title = "店铺code")
    private String shopCode;
    @Schema(hidden = true, title = "积分变更方式，需在忠诚度字典表中预设，若传入忠诚度不存在的值会出现编码转换异常")
    private String changeMode;
    @Schema(hidden = true, title = "幂等方式, 0-临时redis、1-永久数据库，默认值为1")
    private Integer idempotentMode = 1;
    @Schema(hidden = true, title = "获取redis锁的超时时间，单位ms。锁等待时间")
    private Integer lockWaitTime;
    @Schema(title = "活动名称")
    private String actionName;
    @Schema(title = "变更类型: SEND-发放积分  DEDUCT-消耗积分", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 6)
    @NotEmpty
    private String modifyType;
    @Schema(title = "积分值",  requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Integer point;
    @Schema(title = "8时区积分生效时间(扣减场景无需传入),格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectTime;//扣减不需要传参
    @Schema(title = "8时区积分过期时间(扣减场景无需传入),格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 19:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiredTime;//扣减不需要传参
    @Schema(title = "扩展字段一")
    private String KZZD1;
    @Schema(title = "扩展字段二")
    private String KZZD2;
    @Schema(title = "扩展字段三")
    private String KZZD3;
    @Schema(hidden = true, title = "是否可撤销")
    private Boolean reversible = true;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_POINT_MODIFY;
    }
}
