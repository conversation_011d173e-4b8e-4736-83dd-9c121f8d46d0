package com.shuyun.fast.v1_0_0.param.guide;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class GuideBindParam extends ApiBaseParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", required = true)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "现绑定导购工号", required = true)
    @NotBlank
    private String guideId;
    @Schema(title = "原绑定导购工号")
    private String originalGuideId;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_GUIDE_BIND;
    }
}
