package com.shuyun.fast.v1_0_0.result;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.domain.SelectorData;
import com.shuyun.ticket.base.domain.TicketScene;
import com.shuyun.ticket.base.domain.TicketState;
import com.shuyun.ticket.enums.TransferState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class CouponGetResult extends SelectorData {
    @Schema(title = "卡券实例ID")
    private String id;
    @Schema(title = "券项目id")
    private String projectId;
    @Schema(title = "券码")
    private String code;
    @Schema(hidden = true, title = "方案ID")
    private String programId;
    @Schema(title = "8时区发放时间", description = "格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime grantAt;
    @Schema(title = "8时区生效时间", description = "格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveAt;
    @Schema(title = "8时区核销时间", description = "格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useAt;
    @Schema(title = "8时区失效时间", description = "格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiredAt;
    @JsonAlias("state")
    @Schema(title = "卡券状态", description = "CREATED:已发放,ACTIVATED:已启用,DISCARDED:已作废,EFFECTED:已生效,EXPIRED:已失效,LOCKED:已锁定,USED:已使用")
    private TicketState status;
    @Schema(title = "可用次数限制")
    private Long useCountRestrict;
    @Schema(title = "已使用次数")
    private Long usedCount;
    @Schema(title = "券项目规则描述", description = "卡券项目description")
    private String ruleText;
    @Schema(title = "优惠券名称")
    private String couponName;
    @Schema(title = "卡券类型", description = "EXCHANGE:兑换券,INTERNAL_PURCHASE:内购券;DISCOUNT:优惠券")
    private String projectType;
    @Schema(title = "优惠类型", description = "GIFT:商品兑换券;SERVICE:服务兑换券;RATIO_OFF:折扣券;AMOUNT_OFF:满减券")
    private String couponType;
    @Schema(title = "满减金额", description = "优惠面额-指定代金效果")
    private BigDecimal reductAmount;
    @Schema(title = "折扣比率", description = "优惠面额-指定折扣效果")
    private BigDecimal discountRate;
    @Schema(title = "门槛金额", description = "门槛值-订单最低消费金额")
    private BigDecimal thresholdValue;
    @Schema(title = "优惠金额", description = "")
    private BigDecimal denomination;
    @Schema(title = "发放原因")
    private String grantReason;
    @Schema(title = "发放店铺")
    private String grantShop;
    @Schema(title = "卡券实例扩展属性")
    private Map extension;
    @Schema(title = "券项目响应信息")
    private CouponProject project;
    @Schema(title = "赠送状态", description = "WAITING:待领取,RECEIVED:已领取,RETURNED:已退回,CANCELLED:已取消,TIMEOUT:已超时")
    private TransferState transferState;
    @Schema(title = "核销店铺")
    private String useShop;
    @Schema(title = "订单Id")
    private String useOrderId;
    @Schema(title = "8时区启用时间", description = "格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activateAt;
    @Schema(title = "发放平台")
    private String grantPlatform;
    @Schema(title = "场景", description = "import:导入,create:创建,recycle:回收,grant:发放,transfer:赠送,receive:受赠,return:退回,transfer_timeout:赠送超时,activate:启用,deactivate:停用,bind:绑定,unbind:解绑,change_binding:换绑,change_effective:设置有效期,effective:生效,expire:失效,expire_notify:失效提醒,discard:作废,sleep:休眠,wakeup:唤醒,report_loss:挂失,cancel_report_loss:取消挂失,cancel:注销,freeze:冻结,unfreeze:解冻,lock:锁定,unlock:解锁,lock_timeout:锁定超时,destroy:超时,query_handle_result:查询处理结果")
    private TicketScene scene;
    @Schema(title = "上一个状态", description = "CREATED:已发放,ACTIVATED:已启用,DISCARDED:已作废,EFFECTED:已生效,EXPIRED:已失效,LOCKED:已锁定,USED:已使用")
    private TicketState lastState;
    @Schema(title = "受赠人,只有赠送状态是WAITING:待领取时,该值不为空")
    private String receiver;
    @Schema(title = "已赠送次数")
    private Integer transferredCount;
    @Schema(title = "赠送次数限制")
    private Integer transferredCountRestrict;
    @Schema(description = "请求参数中-根据子订单判断实例是否可用是true,有效的券实例才会返回可用的子订单号", title = "实例可用的子订单ID列表-可用列表查询接口时才会返回数据")
    private List<String> usableOrderItemIds;
    @Schema(hidden = true, description = "请求参数中-是否显示不可用的券实例是true,才会返回不可用券实例以及失败原因", title = "返回券不可用的原因-可用列表查询接口时才会返回数据")
    private String unavailabilityReason;
    @Schema(title = "麒麟会员ID")
    private String memberId;
}
