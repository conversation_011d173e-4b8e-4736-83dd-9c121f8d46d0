package com.shuyun.fast.v1_0_0.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class GuideDetail {
    @Schema(title = "导购工号")
    private String employeeId;
    @Schema(title = "成员id")
    private String userId;
    @Schema(title = "导购姓名")
    private String guideName;
    @Schema(title = "手机号")
    private String mobile;
    @Schema(title = "职位 REGIONAL_MANAGE 区域经理,SHOP_MANAGE 店长,GUIDE:导购")
    private String job;
    @Schema(title = "导购状态 1=在职，2=已禁用，4=未激活，5=离职")
    private String status;
    @Schema(title = "导购专属码")
    private String guideCpQrCode;
    @Schema(title = "企微二维码")
    private String cpQrCode;
    @Schema(title = "企微头像")
    private String avatar;
    @Schema(title = "管理区域")
    private List<Map<String, String>> manageAreaList;
    @Schema(title = "管理店铺")
    private List<Map<String, String>> manageShopList;
    @Schema(title = "所属门店")
    private List<Map<String, String>> belongShop;
    @Schema(hidden = true)
    private String cpStatus;

}
