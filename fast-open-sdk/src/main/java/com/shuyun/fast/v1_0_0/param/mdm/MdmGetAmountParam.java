package com.shuyun.fast.v1_0_0.param.mdm;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Introspected
public class MdmGetAmountParam {
    //{"gradeHierarchyId":"60016","gradeRuleTypes":["UPGRADE"],"memberId":"P20240618WKMNSM","targetGradeIds":["60018"]}

    @Schema(title = "会员ID", example = "P202406201GXQST")
    private String memberId;

    @Schema(title = "等级体系ID", example = "60004")
    private Long gradeHierarchyId;

    @Schema(title = "等级规则类型", example = "[UPGRADE]")
    private List<String> gradeRuleTypes;

    @Schema(title = "目标等级id", example = "[60007]")
    private List<Long> targetGradeIds;
}
