package com.shuyun.fast.v1_0_0.param.nancent;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @title member
 * @description 奇门会员传输入参
 * @create 2024/11/25 15:49
 */

@Data
@Introspected
public class Member {
    @Schema(title = "会员首次绑卡时间(非等级下行此字段必定有值)",example = "2022-01-01 00:00:00")
    private String firstBindCardTime;
    @Schema(title = "会员最近一次绑卡时间",example = "2022-01-01 00:00:00")
    private String lastedBindCardTime;
    @Schema(title = "会员最近一次解卡时间",example = "2022-01-01 00:00:00")
    private String lastedUnbindCardTime;
    @Schema(title = "会员等级(非等级下行此字段无值)",example = "1")
    private String level;
    @Schema(title = "密文手机号",example = "b9c44fb6900479ea81f4669b368ea4dd")
    private String mixMobile;
    @Schema(title = "公司级客户唯一标识",example = "AAHuAQHuAACnqjEAAbLR8826")
    private String omid;
    @Schema(title = "店铺会员唯一标识",example = "AAHuAQHuAACnqjEAAbLR8826")
    private String ouid;
    @Schema(title = "会员积分",example = "10.00")
    private String point;
    @Schema(title = "会员扩展属性")
    private Extend extend;
    @Schema(title = "卖家昵称")
    private String sellerNick;
    @Schema(title = "南讯ouid",example = "~U9MGVNd4qgKiLsC67e5BWQ==~1~")
    private String nasOuid;

}
