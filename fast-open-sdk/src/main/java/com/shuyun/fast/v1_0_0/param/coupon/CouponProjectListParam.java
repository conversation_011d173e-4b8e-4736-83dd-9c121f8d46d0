package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponProjectListParam extends ApiBaseParam {

    @Schema(title = "当前页:从0开始", requiredMode = Schema.RequiredMode.REQUIRED, example = "0", defaultValue = "0")
    private Integer page = 0;
    @Schema(title = "页大小:不超过20", requiredMode = Schema.RequiredMode.REQUIRED, example = "20", defaultValue = "20")
    private Integer pageSize = 20;
    @Schema(title = "查询条件参数", description = "查询条件参数是动态传入,例子:{\"title\":{\"EQ\":\"openapi测试项目\"},\"status\":{\"EQ\":\"RELEASED\"},\"branch\":{\"IN\":[\"2e3cc427\"]},\"updateAt\":{\"BETWEEN\":[\"2022-10-21 14:24:28\",\"2022-10-21 14:24:30\"]}}，可通过响应结果值中的属性名称过滤")
    private String queryParams;
    @Schema(title = "排序字段", defaultValue = "CREATED")
    private String sortBy = "created";
    @Schema(title = "排序类型:ASC、DESC,不传默认为DESC", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 5)
    @NotBlank
    private String sortType = "desc";

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_PROJECT_LIST;
    }
}
