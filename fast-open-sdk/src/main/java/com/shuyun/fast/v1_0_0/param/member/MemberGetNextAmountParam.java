package com.shuyun.fast.v1_0_0.param.member;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Introspected
@AllArgsConstructor
public class MemberGetNextAmountParam {

    @Schema(title = "会员ID", example = "P202406201GXQST")
    @NotNull
    private String memberId;
    @Schema(title = "等级业务类型,不传,默认为: GRADE", defaultValue = "GRADE")
    private String gradeBizType = "GRADE";
    @Schema(title = "等级体系id,不传,默认为: 60004", defaultValue = "60004")
    private Long gradeHierarchyId = 60004L;
}
