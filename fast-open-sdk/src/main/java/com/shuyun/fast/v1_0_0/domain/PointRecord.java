package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class PointRecord extends MemberId {

    @Schema(title = "流水号", example = "12dwdw")
    private String id;
    @Schema(title = "历史总积分")
    private Double totalPoint;
    @Schema(title = "积分变更类型 SEND: 立即发放, DELAY_SEND: 延迟发放, EXPIRE: 过期, FREEZE: 冻结, UNFREEZE: 取消冻结, DEDUCT: 扣减, ABOLISH: 作废, TIMER: 定时, RECALCULATE: 废弃重算, SPECIAL_DEDUCT: 特殊扣除, SPECIAL_FREEZE: 特殊冻结, SPECIAL_UNFREEZE: 特殊解冻, SPECIAL_ABOLISH: 特殊废弃, MANUAL_ABOLISH: 手动废弃,OPEN_FREEZE: 接口冻结, OPEN_UNFREEZE: 接口解冻", example = "SEND")
    private String recordType;
    @Schema(title = "备注", example = "备注")
    @JsonAlias("desc")
    private String description;
    @Schema(hidden = true, title = "业务类型",example = "MANUAL")
    private String changeMode;
    @Schema(title = "追溯id", example = "12dwdw")
    private String traceId;
    @Schema(title = "渠道类型", example = "TAOBAO")
    private String channelType;
    @Schema(title = "Key")
    private String key;
    @Schema(title = "店铺ID")
    @JsonAlias("shopId")
    private String shopCode;
    @Schema(title = "变更积分")
    private Double point;
    @Schema(title = "变更积分(负数带负号)")
    private Double signedPoint;
    @Schema(title = "8时区积分变动时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;
    @Schema(title = "8时区积分生效时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectTime;
    @Schema(title = "8时区积分过期时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiredTime;
    @Schema(title = "记录来源详情")
    private String recordSourceDetail;
    @Schema(hidden = true, title = "状态")
    private String status;
    @Schema(hidden = true, title = "订单ID等多种信息")
    private String extralInfo;
    @Schema(title = "扩展字段一")
    private String KZZD1;
    @Schema(title = "扩展字段二")
    private String KZZD2;
    @Schema(title = "扩展字段三")
    private String KZZD3;
}
