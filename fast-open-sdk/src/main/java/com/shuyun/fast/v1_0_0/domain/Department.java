package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Department {
    @Schema(hidden = true, title = "主键id")
    private String id;
    @Schema(title = "部门编码", example = "150150965")
    private String code;
    @Schema(title = "部门名称", example = "TAOBAO")
    private String name;
    @Schema(title = "父级编码", example = "TAOBAO")
    private String parentId;
    @Schema(title = "创建日期", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @Schema(title = "最后同步日期", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync;
    @Schema(title = "部门层级", example = "1")
    private Integer level;
    @Schema(title = "是否有效", example = "true")
    private Boolean enable;
    @Schema(title = "门店编码", example = "FSSD")
    private String shopCode;
    @Schema(title = "组织类型 6店铺 5地区品牌 4零售店铺 3分公司 2大区 1零售大区", example = "2")
    //对应Org的组织类型--------7门店 6地区品牌 5零售店铺 4城市 3分公司 2零售大区 1全公司
    private Long type;
}
