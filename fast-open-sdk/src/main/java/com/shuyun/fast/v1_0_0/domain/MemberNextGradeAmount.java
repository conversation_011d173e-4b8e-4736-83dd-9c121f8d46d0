package com.shuyun.fast.v1_0_0.domain;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@Introspected
public class MemberNextGradeAmount {
    @Schema(title = "距离下一等级消费金额", example = "5000")
    private String diff;
    @Schema(title = "下一等级会员名称", example = "明星会员")
    private String nextGradeName;
}
