package com.shuyun.fast.v1_0_0.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class SelectorData {
    @Schema(title = "核销限制:指定店铺集合-选择器数据")
    private List<String> includeShops;
    @Schema(title = "核销限制:排除店铺集合-选择器数据")
    private List<String> excludeShops;
    @Schema(title = "核销限制:指定商品集合-选择器数据")
    private List<String> includeGoods;
    @Schema(title = "核销限制:排除商品集合-选择器数据")
    private List<String> excludeGoods;
    @Schema(title = "核销限制:所有商品属于-选择器数据")
    private List<String> anyIncludeGoods;
    @Schema(title = "核销限制:存在商品不属于-选择器数据")
    private List<String> anyExcludeGoods;
    @Schema(title = "核销限制:兑换商品集合-选择器数据")
    private List<String> exchangeGoods;
}
