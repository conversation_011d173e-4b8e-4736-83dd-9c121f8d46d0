package com.shuyun.fast.v1_0_0.param.mdm;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotEmpty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@JsonFilter("mdmProductSyncParamFilter")
public class MdmProductSyncParam extends ApiBaseParam implements ModelTag {
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(hidden = true, title="渠道类型", example = "TAOBAO")
    private String channelType;
    @NotEmpty
    @Schema(title="商品编号", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 20, example = "1000101")
    private String productCode;
    @NotEmpty
    @Schema(title="商品名称", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255, example = "外套")
    private String productName;
    //@NotEmpty
    @Schema(title="大类code", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64, example = "001")
    private String deptCode;
    //@NotEmpty
    @Schema(title="大类名称", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255, example = "男装")
    private String deptName;
    //@NotEmpty
    @Schema(title="中类Code", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64, example = "MIDDLE")
    private String familyCode;
    //@NotEmpty
    @Schema(title="中类名称", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255, example = "休闲男装")
    private String familyName;
    //@NotEmpty
    @Schema(title="小类Code", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64, example = "COAT")
    private String subFamilyCode;
    //@NotEmpty
    @Schema(title="小类名称", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255, example = "外套")
    private String subFamilyName;
    @Schema(hidden = true, title="商品品牌", example = "shuyun")
    private String brand;
    @Schema(title="颜色", example = "RED")
    private String color;
    @Schema(title="条形码", example = "10001")
    private String eanCode;
    @Schema(title="材质", example = "PureCotton")
    private String material;
    @Pattern(regexp = "(19|20)[0-9]{2}")
    @Schema(title="上市年份", example = "1980")
    private String onSaleYear;
    @Schema(title="上市日期", example = "2020-01-07")
    private String onSaledate;
    @Schema(title="商品图片URL", example = "[\"http://www.xx.com/url?id=13256\"]")
    private List<String> picture;
    @Schema(title="零售价", example = "20")
    private Double retailPrice;
    @Schema(title="季节", example = "WINTER")
    private String season;
    @Schema(title="尺码", example = "20")
    private String size;
    @Schema(title="商品批次编码", example = "10001")
    private String sqId;
    @Schema(title="吊牌价", example = "20")
    private Double tagPrice;
    @Schema(title="商品描述", example = "性价比高 超值")
    private String productDesc;
    @Schema(hidden = true, title="是否有效, 默认为 Y", example = "Y")
    @NotEmpty
    private String isValid = "Y";
    @Schema(title = "8时区创建时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @Schema(title = "8时区更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();
    @Schema(title="扩展字段", example = "{\"string\":\"update\"}")
    private Map<String, Object> extension;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MDM_PRODUCT_SYNC;
    }

    @Override
    public String fqn() {
        return ModelTags.DATA_FQN_MDM_PRODUCT;
    }
}
