package com.shuyun.fast.v1_0_0.domain;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: GiftSpecificationsSPI
 * @Description:
 * @Author: chendu
 * @Date: 2025年07月09 15:20:42
 */
@Data
public class GiftSpecificationsSPI {
    private String productCode;
    private String productName;
    private String skuID;
    private String skuName;
    private String productDesc;
    private BigDecimal tagPrice;
    private Double retailPrice;
    private String deptCode;
    private String deptName;
    private String familyCode;
    private String familyName;
    private String subFamilyCode;
    private String subFamilyName;
    private String material;
    private String season;
    private String brand;
    private String size;
    private String color;
    private Integer totalInventory;
    private Integer availableInventory;
    private ShelfStatus status;
    private String createTime;
    private String updateTime;
    private String lastSyncDateTime;
    private String onSaleYear;
    private String onSaleDate;
    private List<String> picture;
    private String eanCode;
    private String sqId;
    private String channelID;
}
