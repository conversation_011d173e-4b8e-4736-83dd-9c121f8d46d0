package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChannelMember extends MemberId{
    @Schema(hidden = true, title = "主键id")
    private String id;
    @Schema(hidden = true, title = "会员品牌")
    private String brand;
    @Schema(title = "入会渠道类型", example = "TAOBAO")
    @JsonAlias("enrollChannel")
    private String registerChannel;

    @Schema(title = "经营单位编码", example = "taobaoshop1")
    private String enrollShopCode;
    @Schema(title = "经营单位名称", example = "淘宝旗舰店")
    private String enrollShopName;
    @Schema(title = "注册店铺code", example = "taobaoshop1")
    private String registerShopCode;
    @Schema(title = "注册店铺名称", example = "淘宝旗舰店")
    private String registerShopName;

    @Schema(title = "归属渠道", example = "ELme")
    private String ascriptionChannel;
    @Schema(title = "归属渠道名称", example = "饿了么")
    private String ascriptionChannelName;
    @Schema(title = "归属门店code", example = "TA1002")
    private String ascriptionShopCode;
    @Schema(title = "归属门店名称", example = "淘宝旗舰店")
    private String ascriptionShopName;
    @Schema(title = "会员姓名", example = "张小图")
    private String fullName;
    @Schema(title = "性别： F:女, M:男, O:其他")
    private String gender;
    @Schema(title = "手机号")
    private String mobile;
    @Schema(title = "邮箱")
    private String email;
    @Schema(title = "出生年月日")
    private String dateOfBirth;
    @Schema(title = "婚姻状态， M:已婚 S:未婚 D:离异 O:其他")
    private String marriage;
    @JsonAlias("channelStatus")
    @Schema(title = "会员状态", description = "ACTIVE-生效、INACTIVE-失效、FROZEN-冻结，默认ACTIVE")
    private String status;
    @Schema(title = "渠道id")
    @JsonAlias("customerNo")
    private String userId;
    @Schema(title = "渠道联合id")
    private String unionId;
    @Schema(title = "应用code")
    private String appType;
    @Schema(title = "微信appId")
    private String appId;
    @Schema(title = "微信openId")
    private String openId;
    @Schema(title = "密文手机号")
    private String mixMobile;
    @Schema(title = "昵称")
    private String nick;
    @Schema(title = "渠道会员行为")
    private String action;
    @JsonAlias("triggerTime")
    @Schema(title = "行为时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actionTime;
    @Schema(hidden = true, title = "可选字段的查询结果集")
    private Map optionalFieldData;
}
