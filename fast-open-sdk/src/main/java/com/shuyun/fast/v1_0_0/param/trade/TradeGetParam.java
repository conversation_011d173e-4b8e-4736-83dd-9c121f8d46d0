package com.shuyun.fast.v1_0_0.param.trade;


import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.base.PageParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class TradeGetParam extends PageParam implements ModelTag {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "订单类型:NORMAL-订单  REFUND-退单")
    private String orderType;
    @Schema(title = "订单状态(多个状态以,分隔)")
    private String status;
    @Schema(title = "渠道")
    private List<String> channels;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TRADE_GET;
    }

    @Override
    public String fqn() {
        return ModelTags.DATA_FQN_TRADE_MAIN_ORDER;
    }
}
