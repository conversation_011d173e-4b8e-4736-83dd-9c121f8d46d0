package com.shuyun.fast.v1_0_0.param.history;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PrepareRequest {
    private long limit;
    private long startPage;
    private long endPage;
    private boolean test;
    private String type;
    private String tableName;
    private String hisTableName;
    private String migrationId;
    private long planId;
    private long pointId;
    private boolean overrideHistoryPoint;

   /* @Override
    public String toString() {
        return JSONObjec.toJSONString(this, SerializerFeature.WriteMapNullValue);
    }*/
}
