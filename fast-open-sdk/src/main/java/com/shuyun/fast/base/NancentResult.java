package com.shuyun.fast.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

import static com.shuyun.fast.base.ApiTags.API_RESP_SUCCESS;

/**
 * <AUTHOR>
 * @title NancentResult
 * @description 南讯奇门接口返回
 * @create 2024/11/25 15:47
 */
@Data
public class NancentResult implements Serializable {
    @Schema(title = "返回状态码：SUCCESS/FAIL SUCCESS表示商户接收通知成功并校验成功,FAIL表示失败")
    @JsonProperty("return_code")
    private String return_code;
    @Schema(title = "返回信息")
    @JsonProperty("return_msg")
    private String return_msg;

    public NancentResult(String return_code,
                         String return_msg){
        this.return_code = return_code;
        this.return_msg = return_msg;
    }

    public static NancentResult success(){
        return new NancentResult("SUCCESS",null);
    }

    public static NancentResult fail(String message){
        return new NancentResult("FAIL",message);
    }


}
