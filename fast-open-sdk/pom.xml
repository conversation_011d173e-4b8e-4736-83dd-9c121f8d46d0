<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shuyun</groupId>
        <artifactId>fast-integration</artifactId>
        <version>0.1.0</version>
    </parent>

    <artifactId>fast-open-sdk</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <benefit.version>1.17.0</benefit.version>
        <mbsp.version>1.12.0</mbsp.version>
        <loyalty.version>LP3.23.12</loyalty.version>
        <cdp-sdk.version>1.42.0.RC1</cdp-sdk.version>
        <kotlin.version>1.9.23</kotlin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--忠诚度api依赖-->
        <dependency>
            <groupId>com.shuyun.loyalty4</groupId>
            <artifactId>loyalty-sdk-api</artifactId>
            <version>${loyalty.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun</groupId>
                    <artifactId>motor-client-annotation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--会籍api依赖-->
        <dependency>
            <groupId>com.shuyun.kylin.member</groupId>
            <artifactId>mbsp-api-core</artifactId>
            <version>${mbsp.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-validation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--储值api依赖-->
        <dependency>
            <groupId>com.shuyun.ticket</groupId>
            <artifactId>prepaid-card-base</artifactId>
            <version>${benefit.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>jakarta.ws.rs</groupId>
                    <artifactId>jakarta.ws.rs-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.lite.module</groupId>
                    <artifactId>lite-server-base</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jaxb-runtime</artifactId>
                    <groupId>org.glassfish.jaxb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jakarta.xml.bind-api</artifactId>
                    <groupId>jakarta.xml.bind</groupId>
                </exclusion>
                <exclusion>
                    <groupId>jakarta.validation</groupId>
                    <artifactId>jakarta.validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--卡券api依赖-->
        <dependency>
            <groupId>com.shuyun.ticket</groupId>
            <artifactId>benefit-base</artifactId>
            <version>${benefit.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun.ticket</groupId>
                    <artifactId>ticket-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jakarta.validation</groupId>
                    <artifactId>jakarta.validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--cdp sdk 依赖-->
        <dependency>
            <groupId>com.shuyun.cdp</groupId>
            <artifactId>cdp-sdk</artifactId>
            <version>${cdp-sdk.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>javax.ws.rs-api</artifactId>
                    <groupId>javax.ws.rs</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.dm</groupId>
                    <artifactId>dm-sdk-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.dm</groupId>
                    <artifactId>dm-dataapi-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>


</project>