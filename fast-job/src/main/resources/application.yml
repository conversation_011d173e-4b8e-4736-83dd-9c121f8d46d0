micronaut:
  application:
    name: fast-job
  server:
    port: 8080
    context-path: /fast-job
  executors:
    blocking:
      type: fixed
      number-of-threads: 64
    job:
      type: fixed
      number-of-threads: 128
  caches:
    firstBizCache:
      charset: UTF-8
      maximum-size: 1000
#      expire-after-access: 10s
    secondBizCache:
      charset: UTF-8
      maximum-size: 1000
#      expire-after-access: 10s
    firstProjectCache:
      charset: UTF-8
      maximum-size: 5000
#      expire-after-access: 10s
    secondProjectCache:
      charset: UTF-8
      maximum-size: 5000
#      expire-after-access: 10s
    selectorCache:
      charset: UTF-8
      maximum-size: 10000
#      expire-after-access: 10s
  openapi:
    views:
      #编译期生成swagger-ui所需文件
      spec: swagger-ui.enabled=true
  router:
    static-resources:
      swagger:
        paths: classpath:META-INF/swagger
        mapping: /swagger/**
      swagger-ui:
        paths: classpath:META-INF/swagger/views/swagger-ui
        mapping: /swagger-ui/**

  http:
    client:
      ssl:
        insecure-trust-all-certificates: true

logger:
  config: logback.xml