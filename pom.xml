<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.shuyun</groupId>
    <artifactId>fast-integration</artifactId>
    <version>0.1.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>fast-open-sdk</module>
        <module>fast-core</module>
        <module>fast-event</module>
        <module>fast-job</module>
        <module>fast-service</module>
    </modules>

    <parent>
        <groupId>io.micronaut</groupId>
        <artifactId>micronaut-parent</artifactId>
        <version>3.10.1</version>
    </parent>
    <properties>
        <packaging>jar</packaging>
        <jdk.version>11</jdk.version>
        <release.version>11</release.version>
        <micronaut.version>3.10.1</micronaut.version>
        <flyway.version>8.2.0</flyway.version>
        <micronaut.aot.enabled>false</micronaut.aot.enabled>
        <micronaut.aot.packageName>com.shuyun.aot.generated</micronaut.aot.packageName>
        <micronaut.runtime>netty</micronaut.runtime>
    </properties>

    <distributionManagement>
        <repository>
            <id>nexus</id>
            <name>Nexus</name>
            <url>http://ci.yunat.com:8091/nexus/content/repositories/releases</url>
        </repository>
    </distributionManagement>

    <dependencies>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-validation</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-validation</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <useIncrementalCompilation>true</useIncrementalCompilation>
                    <annotationProcessorPaths combine.self="override">
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut</groupId>
                            <artifactId>micronaut-inject-java</artifactId>
                            <version>${micronaut.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut</groupId>
                            <artifactId>micronaut-http-validation</artifactId>
                            <version>${micronaut.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut.data</groupId>
                            <artifactId>micronaut-data-processor</artifactId>
                            <version>${micronaut.data.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut.openapi</groupId>
                            <artifactId>micronaut-openapi</artifactId>
                            <version>${micronaut.openapi.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-Amicronaut.processing.group=com.shuyun.fast</arg>
                        <arg>-Amicronaut.processing.module=fast-integration</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
